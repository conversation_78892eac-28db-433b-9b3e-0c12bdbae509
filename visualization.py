#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Visualization Handler Module

This module provides advanced visualization capabilities for geographic data analysis.
It creates interactive visualizations including histograms, scatter plots, correlation
heatmaps, 3D visualizations, and time-series animations with unit-level analysis.

The visualizations are designed to provide insights into spatial and temporal patterns
in the data, with consistent styling, interactive features, and detailed explanations.

Author: Data Analysis Team
Version: 1.0
"""

# Visualization libraries
import plotly.express as px
import plotly.graph_objs as go
import plotly.figure_factory as ff
from plotly.subplots import make_subplots

# Data processing libraries
import pandas as pd
import numpy as np
from scipy import stats

# Network analysis
import networkx as nx

# Utility libraries
import re
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# Configure logging with detailed format for debugging and tracking
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class VisualizationHandler:
    """
    A class for creating advanced interactive visualizations for geographic data analysis.

    This class provides methods for generating various types of visualizations including
    histograms, scatter plots, correlation heatmaps, 3D visualizations, and animated
    time-series. It maintains consistent styling across visualizations and provides
    detailed explanations for each visualization type.

    The visualizations are designed to provide insights into spatial and temporal patterns
    in the data, with a focus on unit-level analysis and interactive features.

    Attributes:
        color_scheme (list): Default color palette for visualizations
        plot_template (str): Default Plotly template for consistent styling
        default_width (int): Default width for visualizations in pixels
        default_height (int): Default height for visualizations in pixels
        default_aspect_ratio (float): Default aspect ratio for visualizations
        unit_colors (dict): Mapping of unit IDs to consistent colors
        font_settings (dict): Default font settings for visualizations
    """

    def __init__(self):
        """
        Initialize the VisualizationHandler with default settings and color schemes.

        Sets up default visualization parameters including color schemes, plot templates,
        dimensions, and font settings to ensure consistent styling across all visualizations.
        """
        logger.info("Initializing VisualizationHandler")

        # Default color palette for visualizations
        self.color_scheme = px.colors.qualitative.Set3

        # Default template for consistent styling
        self.plot_template = "plotly_white"

        # Default dimensions and aspect ratio
        self.default_width = 1200
        self.default_height = 800
        self.default_aspect_ratio = 16/9  # Widescreen format

        # Dictionary to store unit-to-color mappings for consistency
        self.unit_colors = {}

        # Default font settings for all text elements
        self.font_settings = dict(
            family="Arial, sans-serif",
            size=12,
            color="#2f2f2f"  # Dark gray for better readability
        )

    def _initialize_unit_colors(self, df: pd.DataFrame) -> None:
        """
        Initialize consistent colors for units across all visualizations.

        This method creates a mapping between unit IDs and colors to ensure that
        each unit is represented by the same color across all visualizations.
        This consistency helps users identify and track specific units across
        different visualization types.

        Args:
            df (pd.DataFrame): Input DataFrame containing unit_place_id column

        Raises:
            Exception: If there's an error during color mapping initialization
        """
        try:
            logger.info("Initializing unit color mapping")

            # Get all unique unit IDs from the DataFrame
            unique_units = df['unit_place_id'].unique()

            # Create a color list long enough to accommodate all units
            # by repeating the color palette if necessary
            colors = px.colors.qualitative.Set3 * (len(unique_units) // len(px.colors.qualitative.Set3) + 1)

            # Create a dictionary mapping each unit ID to a specific color
            self.unit_colors = dict(zip(unique_units, colors[:len(unique_units)]))

            logger.info(f"Successfully mapped colors for {len(unique_units)} units")
        except Exception as e:
            # Log the error and re-raise to allow caller to handle it
            logger.error(f"Error initializing unit colors: {str(e)}")
            raise

    def apply_global_filter(self, df: pd.DataFrame, global_filter: dict) -> tuple:
        """
        Apply global filter to a dataframe and generate a filter title.

        This is a convenience method that applies the hierarchical filter and generates
        a user-friendly filter title for visualizations.

        Args:
            df (pd.DataFrame): Input dataframe to filter
            global_filter (dict): Global filter from treemap selection

        Returns:
            tuple: (filtered_df, filter_title, entity_type, entity_id)
        """
        filter_title = ""
        entity_type = ""
        entity_id = ""

        if not global_filter:
            return df, filter_title, entity_type, entity_id

        # Apply the hierarchical filter
        filtered_df = self._apply_hierarchical_filter(df, global_filter)

        # Extract entity information for the title
        entity_type = global_filter['type']
        entity_id = global_filter['id']

        # Format the entity ID for display
        if isinstance(entity_id, str) and ': ' in entity_id:
            display_id = entity_id.split(': ')[1]
        else:
            display_id = entity_id

        # Create a user-friendly filter title
        if len(str(display_id)) > 8:
            display_id = str(display_id)[-8:]
        filter_title = f" for {entity_type.capitalize()} {display_id}"

        # Check if the filtered dataframe is empty
        if filtered_df.empty:
            logger.warning(f"No data found for {entity_type} {entity_id}")

        return filtered_df, filter_title, entity_type, entity_id

    def _apply_hierarchical_filter(self, df: pd.DataFrame, filter_entity: dict) -> pd.DataFrame:
        """
        Apply hierarchical filtering to a dataframe based on the selected entity.

        This function filters the dataframe based on the type and ID of the selected entity
        in the hierarchical structure (complex → building → unit → address).
        It serves as the central filtering mechanism for all visualizations.

        Args:
            df (pd.DataFrame): Input dataframe to filter
            filter_entity (dict): Dictionary containing filter information with keys:
                - 'type': The type of entity ('complex', 'building', 'unit', or 'address')
                - 'id': The ID of the entity to filter by
                - 'actual_id': The actual ID without the type prefix (optional)
                - 'hierarchical_context': Dictionary with parent entity IDs (optional)

        Returns:
            pd.DataFrame: Filtered dataframe
        """
        if not filter_entity or 'type' not in filter_entity or 'id' not in filter_entity:
            return df

        entity_type = filter_entity['type']
        entity_id = filter_entity['id']

        # Use actual_id if provided, otherwise extract it from entity_id
        if 'actual_id' in filter_entity and filter_entity['actual_id']:
            actual_id = filter_entity['actual_id']
        elif isinstance(entity_id, str) and ': ' in entity_id:
            actual_id = entity_id.split(': ')[1]
        else:
            actual_id = entity_id

        logger.info(f"Applying hierarchical filter: {entity_type}={actual_id}")

        # Get hierarchical context if available
        hierarchical_context = filter_entity.get('hierarchical_context', {})
        logger.info(f"Hierarchical context: {hierarchical_context}")

        # Apply the appropriate filter based on entity type
        if entity_type == 'complex' and 'complex_id' in df.columns:
            return df[df['complex_id'] == actual_id]

        elif entity_type == 'building' and 'building_place_id' in df.columns:
            filtered_df = df[df['building_place_id'] == actual_id]

            # If we have complex context and the filtered dataframe is empty, try using the context
            if filtered_df.empty and 'complex_id' in hierarchical_context and 'complex_id' in df.columns:
                complex_id = hierarchical_context['complex_id']
                if isinstance(complex_id, str) and ': ' in complex_id:
                    complex_id = complex_id.split(': ')[1]
                logger.info(f"No direct matches for building {actual_id}, trying with complex context {complex_id}")
                return df[(df['building_place_id'] == actual_id) & (df['complex_id'] == complex_id)]

            return filtered_df

        elif entity_type == 'unit' and 'unit_place_id' in df.columns:
            filtered_df = df[df['unit_place_id'] == actual_id]

            # If we have building/complex context and the filtered dataframe is empty, try using the context
            if filtered_df.empty and hierarchical_context:
                context_filters = [f"unit_place_id == '{actual_id}'"]

                if 'building_id' in hierarchical_context and 'building_place_id' in df.columns:
                    building_id = hierarchical_context['building_id']
                    if isinstance(building_id, str) and ': ' in building_id:
                        building_id = building_id.split(': ')[1]
                    context_filters.append(f"building_place_id == '{building_id}'")

                if 'complex_id' in hierarchical_context and 'complex_id' in df.columns:
                    complex_id = hierarchical_context['complex_id']
                    if isinstance(complex_id, str) and ': ' in complex_id:
                        complex_id = complex_id.split(': ')[1]
                    context_filters.append(f"complex_id == '{complex_id}'")

                if len(context_filters) > 1:
                    context_query = " and ".join(context_filters)
                    logger.info(f"No direct matches for unit {actual_id}, trying with context: {context_query}")
                    return df.query(context_query)

            return filtered_df

        elif entity_type == 'address' and 'address_id' in df.columns:
            filtered_df = df[df['address_id'] == actual_id]

            # If we have unit/building/complex context and the filtered dataframe is empty, try using the context
            if filtered_df.empty and hierarchical_context:
                context_filters = [f"address_id == '{actual_id}'"]

                if 'unit_id' in hierarchical_context and 'unit_place_id' in df.columns:
                    unit_id = hierarchical_context['unit_id']
                    if isinstance(unit_id, str) and ': ' in unit_id:
                        unit_id = unit_id.split(': ')[1]
                    context_filters.append(f"unit_place_id == '{unit_id}'")

                if 'building_id' in hierarchical_context and 'building_place_id' in df.columns:
                    building_id = hierarchical_context['building_id']
                    if isinstance(building_id, str) and ': ' in building_id:
                        building_id = building_id.split(': ')[1]
                    context_filters.append(f"building_place_id == '{building_id}'")

                if 'complex_id' in hierarchical_context and 'complex_id' in df.columns:
                    complex_id = hierarchical_context['complex_id']
                    if isinstance(complex_id, str) and ': ' in complex_id:
                        complex_id = complex_id.split(': ')[1]
                    context_filters.append(f"complex_id == '{complex_id}'")

                if len(context_filters) > 1:
                    context_query = " and ".join(context_filters)
                    logger.info(f"No direct matches for address {actual_id}, trying with context: {context_query}")
                    return df.query(context_query)

            return filtered_df

        # If no valid filter could be applied, return the original dataframe
        logger.warning(f"Could not apply filter for {entity_type}={actual_id}")

        # Log the filtered dataframe size
        filtered_rows = len(df)
        logger.info(f"Hierarchical filter returned {filtered_rows} rows")

        return df

    def _format_change(self, value: float, invert: bool = False) -> str:
        """
        Format a change value with appropriate color and arrow.

        Args:
            value (float): The change value to format
            invert (bool): Whether to invert the interpretation of positive/negative
                           (e.g., for 'Complete' category, an increase is good)

        Returns:
            str: Formatted HTML string with appropriate color and arrow
        """
        if abs(value) < 0.1:
            return "0.0%"  # No significant change

        is_improvement = (value < 0 and not invert) or (value > 0 and invert)
        color = 'green' if is_improvement else 'red'
        arrow = '↓' if value < 0 else '↑'

        return f"<span style='color:{color};font-weight:bold'>{arrow} {abs(value):.1f}%</span>"

    def create_hierarchical_summary_table(self, df: pd.DataFrame, filter_title: str = "") -> go.Figure:
        """
        Create a hierarchical summary table that combines scan discrepancy metrics across all hierarchy levels.

        Args:
            df (pd.DataFrame): DataFrame with scan consistency classification
            filter_title (str): Optional filter title to add to the visualization

        Returns:
            go.Figure: Plotly figure with hierarchical summary table
        """
        try:
            # Identify available hierarchical levels
            hierarchical_levels = []
            if 'complex_id' in df.columns:
                hierarchical_levels.append('complex_id')
            if 'building_place_id' in df.columns:
                hierarchical_levels.append('building_place_id')
            if 'unit_place_id' in df.columns:
                hierarchical_levels.append('unit_place_id')
            if 'address_id' in df.columns:
                hierarchical_levels.append('address_id')

            if not hierarchical_levels:
                # No hierarchical levels found, return a simple message
                fig = go.Figure()
                fig.add_annotation(
                    text="No hierarchical data available for summary table",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5,
                    showarrow=False,
                    font=dict(size=14, color='red')
                )
                return fig

            # Create a list to store hierarchical summary data
            summary_data = []

            # Process each hierarchical level
            for level in hierarchical_levels:
                # Group by the current level and calculate metrics
                level_groups = df.groupby(level)

                # Calculate metrics for each group
                for group_id, group_df in level_groups:
                    # Skip if group_id is None or NaN
                    if pd.isna(group_id) or group_id is None:
                        continue

                    # Calculate scan discrepancy metrics
                    total_records = len(group_df)
                    missing_entry = group_df[~group_df['has_entry']].shape[0]
                    missing_exit = group_df[~group_df['has_exit']].shape[0]
                    missing_delivery = group_df[~group_df['has_delivery']].shape[0]

                    # Calculate percentages
                    missing_entry_pct = (missing_entry / total_records) * 100 if total_records > 0 else 0
                    missing_exit_pct = (missing_exit / total_records) * 100 if total_records > 0 else 0
                    missing_delivery_pct = (missing_delivery / total_records) * 100 if total_records > 0 else 0

                    # Calculate overall discrepancy rate
                    discrepancy_rate = group_df['has_discrepancy'].mean() * 100

                    # Create a row for the summary table
                    row = {
                        'Level': self._format_level_name(level),
                        'ID': str(group_id),
                        'Total Records': total_records,
                        'Discrepancy Rate (%)': discrepancy_rate,
                        'Missing Entry': missing_entry,
                        'Missing Entry (%)': missing_entry_pct,
                        'Missing Exit': missing_exit,
                        'Missing Exit (%)': missing_exit_pct,
                        'Missing Delivery': missing_delivery,
                        'Missing Delivery (%)': missing_delivery_pct
                    }

                    # Add parent IDs if available
                    for parent_level in hierarchical_levels:
                        if parent_level != level and parent_level in group_df.columns:
                            # Get the most common parent ID for this group
                            parent_id = group_df[parent_level].mode().iloc[0] if not group_df[parent_level].empty else None
                            if not pd.isna(parent_id) and parent_id is not None:
                                row[f'Parent {self._format_level_name(parent_level)}'] = str(parent_id)

                    # Add to summary data
                    summary_data.append(row)

            # Convert to DataFrame
            summary_df = pd.DataFrame(summary_data)

            # Sort by hierarchical level and discrepancy rate (highest first)
            level_order = {self._format_level_name(level): i for i, level in enumerate(hierarchical_levels)}
            summary_df['Level_Order'] = summary_df['Level'].map(level_order)
            summary_df = summary_df.sort_values(['Level_Order', 'Discrepancy Rate (%)'], ascending=[True, False])
            summary_df = summary_df.drop(columns=['Level_Order'])

            # Create column order
            column_order = ['Level', 'ID', 'Total Records', 'Discrepancy Rate (%)']
            for scan_type in ['Entry', 'Exit', 'Delivery']:
                column_order.extend([f'Missing {scan_type}', f'Missing {scan_type} (%)'])

            # Add parent columns if they exist
            parent_columns = [col for col in summary_df.columns if col.startswith('Parent ')]
            column_order.extend(parent_columns)

            # Filter columns to only those that exist
            column_order = [col for col in column_order if col in summary_df.columns]

            # Create the table figure
            fig = go.Figure(data=[go.Table(
                header=dict(
                    values=column_order,
                    fill_color='#4a90e2',
                    align='left',
                    font=dict(color='white', size=14)
                ),
                cells=dict(
                    values=[summary_df[col] for col in column_order],
                    fill_color=[
                        ['#f8f9fa'] * len(summary_df),  # Level
                        ['#f8f9fa'] * len(summary_df),  # ID
                        ['#f8f9fa'] * len(summary_df),  # Total Records
                        # Color discrepancy rate by severity
                        [
                            '#e8f5e9' if rate < 5 else  # Green for low discrepancy
                            '#fff9c4' if rate < 15 else  # Yellow for medium discrepancy
                            '#ffcdd2'  # Red for high discrepancy
                            for rate in summary_df['Discrepancy Rate (%)']
                        ],
                        # Color missing entry count by severity
                        [
                            '#e8f5e9' if count == 0 else
                            '#fff9c4' if count < 10 else
                            '#ffcdd2'
                            for count in summary_df['Missing Entry']
                        ],
                        # Color missing entry percentage by severity
                        [
                            '#e8f5e9' if pct < 5 else
                            '#fff9c4' if pct < 15 else
                            '#ffcdd2'
                            for pct in summary_df['Missing Entry (%)']
                        ],
                        # Color missing exit count by severity
                        [
                            '#e8f5e9' if count == 0 else
                            '#fff9c4' if count < 10 else
                            '#ffcdd2'
                            for count in summary_df['Missing Exit']
                        ],
                        # Color missing exit percentage by severity
                        [
                            '#e8f5e9' if pct < 5 else
                            '#fff9c4' if pct < 15 else
                            '#ffcdd2'
                            for pct in summary_df['Missing Exit (%)']
                        ],
                        # Color missing delivery count by severity
                        [
                            '#e8f5e9' if count == 0 else
                            '#fff9c4' if count < 10 else
                            '#ffcdd2'
                            for count in summary_df['Missing Delivery']
                        ],
                        # Color missing delivery percentage by severity
                        [
                            '#e8f5e9' if pct < 5 else
                            '#fff9c4' if pct < 15 else
                            '#ffcdd2'
                            for pct in summary_df['Missing Delivery (%)']
                        ],
                        # Parent columns (if any)
                        *[['#f8f9fa'] * len(summary_df) for _ in parent_columns]
                    ],
                    align='left',
                    font=dict(size=12),
                    format=[
                        None,  # Level
                        None,  # ID
                        None,  # Total Records
                        '.1f',  # Discrepancy Rate (%)
                        None,  # Missing Entry
                        '.1f',  # Missing Entry (%)
                        None,  # Missing Exit
                        '.1f',  # Missing Exit (%)
                        None,  # Missing Delivery
                        '.1f',  # Missing Delivery (%)
                        *[None for _ in parent_columns]  # Parent columns (if any)
                    ]
                )
            )])

            # Update layout
            fig.update_layout(
                title=f"Hierarchical Scan Discrepancy Summary{filter_title}",
                margin=dict(l=20, r=20, t=50, b=20),
                height=600
            )

            return fig
        except Exception as e:
            logger.error(f"Error creating hierarchical summary table: {str(e)}", exc_info=True)
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating hierarchical summary table: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=14, color='red')
            )
            return fig

    def _format_level_name(self, level: str) -> str:
        """
        Format a hierarchical level name for display.

        Args:
            level (str): The column name of the hierarchical level

        Returns:
            str: A formatted, human-readable name for the level
        """
        if level == 'complex_id':
            return 'Complex'
        elif level == 'building_place_id':
            return 'Building'
        elif level == 'unit_place_id':
            return 'Unit'
        elif level == 'address_id':
            return 'Address'
        else:
            # Capitalize and replace underscores with spaces
            return level.replace('_', ' ').title()

    def set_consistent_size(self, fig: go.Figure, width: Optional[int] = None,
                          height: Optional[int] = None, responsive: bool = True) -> None:
        """
        Set consistent size for all visualizations with improved dynamic sizing.

        This method applies consistent sizing to visualizations to ensure a uniform
        appearance across different visualization types. It maintains the specified
        aspect ratio when only one dimension (width or height) is provided.

        The method also applies consistent font settings to all text elements in
        the visualization for a cohesive visual style. When responsive=True, the
        visualization will dynamically resize to fit the container while maintaining
        aspect ratio.

        Args:
            fig (go.Figure): Plotly figure to resize
            width (Optional[int]): Custom width in pixels, defaults to self.default_width if neither width nor height is provided
            height (Optional[int]): Custom height in pixels, calculated from width and aspect ratio if not provided
            responsive (bool): Whether to enable responsive sizing (default: True)

        Raises:
            Exception: If there's an error during figure resizing
        """
        try:
            # If neither width nor height is provided, use default width
            if width is None and height is None:
                width = self.default_width

            # If only width is provided, calculate height based on aspect ratio
            if width is not None and height is None:
                height = int(width / self.default_aspect_ratio)

            # If only height is provided, calculate width based on aspect ratio
            elif height is not None and width is None:
                width = int(height * self.default_aspect_ratio)

            # Update the figure layout with the calculated dimensions and font settings
            layout_update = {
                'font': self.font_settings,  # Apply consistent font settings
                'margin': dict(l=40, r=40, t=60, b=60, pad=4),  # Consistent margins
            }

            # Add size parameters based on responsive setting
            if responsive:
                # For responsive layouts, set autosize to True and use percentage-based sizing
                layout_update.update({
                    'autosize': True,
                    'height': height,  # Initial height
                })
            else:
                # For fixed layouts, set explicit width and height
                layout_update.update({
                    'width': width,
                    'height': height,
                    'autosize': False,
                })

            # Apply the layout updates
            fig.update_layout(**layout_update)

            # Add responsive sizing configuration
            if responsive:
                fig.update_layout(
                    # This ensures the plot maintains its aspect ratio when resized
                    xaxis=dict(
                        scaleanchor="y",
                        constrain="domain"
                    )
                )

        except Exception as e:
            # Log the error and re-raise to allow caller to handle it
            logger.error(f"Error setting figure size: {str(e)}")
            raise

    def add_explanation(self, fig: go.Figure, explanation_text: str, title: str = None) -> None:
        """
        Add explanation text to the figure with show/hide toggle buttons.

        This method adds an explanation text box to the figure that can be shown or hidden
        using toggle buttons. The explanation provides details about what the visualization
        shows, how to interpret it, and what insights can be gained from it.

        Args:
            fig (go.Figure): The figure to add the explanation to
            explanation_text (str): The explanation text to add (can include HTML formatting)
            title (str, optional): Not used anymore to avoid duplication with the figure's title.
                                  Kept for backward compatibility.
        """
        try:
            # Add the explanation as a hidden annotation that appears when the info button is clicked
            fig.add_annotation(
                xref="paper", yref="paper",
                x=0.5, y=0.5,  # Center of the figure
                text=explanation_text,
                showarrow=False,
                align="left",
                bgcolor="rgba(255,255,255,0.95)",
                bordercolor="rgb(70,130,180)",
                borderwidth=2,
                font=dict(size=12),
                visible=False,  # Initially hidden
                opacity=0.9,
                width=400,
                height=300
            )

            # Add a custom button to toggle the explanation visibility
            fig.update_layout(
                updatemenus=[
                    dict(
                        type="buttons",
                        direction="right",
                        buttons=[
                            dict(
                                args=[{"annotations[0].visible": True}],
                                label="Show Info",
                                method="relayout"
                            ),
                            dict(
                                args=[{"annotations[0].visible": False}],
                                label="Hide Info",
                                method="relayout"
                            )
                        ],
                        pad={"r": 10, "t": 10},
                        showactive=True,
                        x=0.98,
                        xanchor="right",
                        y=1.10,
                        yanchor="top"
                    )
                ]
            )
        except Exception as e:
            # Log the error and re-raise to allow caller to handle it
            logger.error(f"Error adding explanation to figure: {str(e)}")
            raise

    def add_timestamp(self, fig: go.Figure) -> None:
        """
        Add generation timestamp to visualization.

        This method adds a small timestamp annotation to the bottom right corner
        of the visualization showing when it was generated. This is useful for
        tracking when visualizations were created, especially in reports or
        dashboards that may be saved or shared.

        Args:
            fig (go.Figure): Plotly figure to annotate

        Raises:
            Exception: If there's an error adding the timestamp annotation
        """
        try:
            # Get the current date and time formatted as a string
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Add an annotation with the timestamp
            fig.add_annotation(
                xref="paper",  # Position relative to the entire figure
                yref="paper",  # Position relative to the entire figure
                x=1,  # Right-aligned
                y=-0.1,  # Below the plot area
                text=f"Generated: {timestamp}",  # The timestamp text
                showarrow=False,  # Don't show an arrow
                align="right",  # Right-align the text
                font=dict(size=8, color="gray")  # Small, gray font for subtlety
            )
        except Exception as e:
            # Log the error and re-raise to allow caller to handle it
            logger.error(f"Error adding timestamp to figure: {str(e)}")
            raise

    def create_enhanced_histograms(self, df: pd.DataFrame, is_cleaned: bool = False, filter_entity: str = None, entity_type: str = None, global_filter: dict = None) -> Dict[str, Any]:
        """
        Create operationally focused histograms for geospatial analysis with filtering options.

        Args:
            df (pd.DataFrame): Input DataFrame
            is_cleaned (bool): Flag indicating if data is cleaned
            filter_entity (str, optional): Entity ID to filter by (building_place_id or unit_place_id)
            entity_type (str, optional): Type of entity to filter by ('building' or 'unit')
            global_filter (dict, optional): Global filter from treemap selection

        Returns:
            Dict[str, Any]: Dictionary containing generated figures
        """
        try:
            logger.info("Creating operationally focused histograms for geospatial analysis")
            self._initialize_unit_colors(df)

            # Apply filtering if specified
            filtered_df = df.copy()
            filter_title = ""

            # First apply global filter if provided (from treemap)
            if global_filter:
                filtered_df, filter_title, entity_type, entity_id = self.apply_global_filter(filtered_df, global_filter)

                if filtered_df.empty:
                    logger.warning(f"No data found for {entity_type} {entity_id}")
                    return {'figures': [], 'message': f"No data found for {entity_type} {entity_id}"}

            # Then apply local filter if provided (from histogram controls)
            elif filter_entity and entity_type:
                if entity_type.lower() == 'building' and 'building_place_id' in df.columns:
                    filtered_df = df[df['building_place_id'] == filter_entity]
                    filter_title = f" for Building {filter_entity[-8:] if len(filter_entity) > 8 else filter_entity}"
                elif entity_type.lower() == 'unit' and 'unit_place_id' in df.columns:
                    filtered_df = df[df['unit_place_id'] == filter_entity]
                    filter_title = f" for Unit {filter_entity[-8:] if len(filter_entity) > 8 else filter_entity}"

                if filtered_df.empty:
                    logger.warning(f"No data found for {entity_type} {filter_entity}")
                    return {'figures': [], 'message': f"No data found for {entity_type} {filter_entity}"}

            # Focus on the most operationally valuable columns only
            relevant_cols = [
                'enter_latitude', 'enter_longitude',  # Entry points are critical for operations
                'centroid_latitude', 'centroid_longitude'  # Centroids provide key location insights
            ]

            # Filter to only include columns that exist in the dataframe
            numeric_cols = [col for col in relevant_cols if col in filtered_df.columns]
            prefix = "Cleaned " if is_cleaned else "Original "

            figs = []
            for col in numeric_cols:
                # Create a simple, focused histogram
                fig = go.Figure()

                # Get overall data statistics
                overall_data = filtered_df[col].dropna()
                overall_stats = overall_data.describe()

                # Add overall histogram with clear styling
                fig.add_trace(go.Histogram(
                    x=overall_data,
                    name="All Data",
                    opacity=0.7,
                    nbinsx=50,  # More bins for better resolution
                    marker_color='rgba(58, 71, 80, 0.6)',
                    hovertemplate=(
                        "Value: %{x}<br>"
                        "Count: %{y}<br>"
                        "<extra></extra>"
                    )
                ))

                # Only add statistical elements if we have enough data
                if len(overall_data) > 1:
                    # Add mean and median lines
                    fig.add_vline(
                        x=overall_stats['mean'],
                        line_dash="dash",
                        line_color="red",
                        annotation_text="Mean",
                        annotation_position="top right"
                    )

                    fig.add_vline(
                        x=overall_stats['50%'],
                        line_dash="dash",
                        line_color="green",
                        annotation_text="Median",
                        annotation_position="top left"
                    )

                    # Add outlier boundaries (1.5 * IQR method)
                    q1 = overall_stats['25%']
                    q3 = overall_stats['75%']
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr

                    # Only add outlier boundaries if they're within the data range
                    if lower_bound > overall_stats['min']:
                        fig.add_vline(
                            x=lower_bound,
                            line_dash="dot",
                            line_color="orange",
                            annotation_text="Lower Outlier Bound",
                            annotation_position="bottom left"
                        )

                    if upper_bound < overall_stats['max']:
                        fig.add_vline(
                            x=upper_bound,
                            line_dash="dot",
                            line_color="orange",
                            annotation_text="Upper Outlier Bound",
                            annotation_position="bottom right"
                        )

                # Add a clear, concise statistics box
                # Format statistics with proper error handling
                mean_val = f"{overall_stats['mean']:.6f}" if 'mean' in overall_stats else "N/A"
                median_val = f"{overall_stats['50%']:.6f}" if '50%' in overall_stats else "N/A"
                std_val = f"{overall_stats['std']:.6f}" if 'std' in overall_stats else "N/A"
                min_val = f"{overall_stats['min']:.6f}" if 'min' in overall_stats else "N/A"
                max_val = f"{overall_stats['max']:.6f}" if 'max' in overall_stats else "N/A"

                stats_text = (
                    f"<b>Key Statistics:</b><br>"
                    f"Mean: {mean_val}<br>"
                    f"Median: {median_val}<br>"
                    f"Std Dev: {std_val}<br>"
                    f"Range: [{min_val}, {max_val}]<br>"
                    f"Count: {len(overall_data)}<br>"
                    f"Missing: {filtered_df[col].isna().sum()}"
                )

                # Add statistics box with improved positioning and styling
                fig.add_annotation(
                    xref="paper",
                    yref="paper",
                    x=0.01,  # Position on the left side instead of right
                    y=0.99,  # Position at the top
                    text=stats_text,
                    showarrow=False,
                    align="left",
                    xanchor="left",
                    yanchor="top",
                    bgcolor="rgba(255, 255, 255, 0.95)",
                    bordercolor="rgba(0, 0, 0, 0.1)",
                    borderwidth=1,
                    font=dict(size=11, family="Arial"),
                    width=180,
                    height=150,
                    opacity=0.9
                )

                # Add building-level analysis if building_place_id exists and we're not already filtering by building
                if 'building_place_id' in filtered_df.columns and not (entity_type == 'building' and filter_entity):
                    # Get top 5 buildings by data count
                    building_counts = filtered_df.groupby('building_place_id')[col].count().sort_values(ascending=False)
                    top_buildings = building_counts.head(5).index.tolist()

                    # Add a box plot comparing these buildings
                    building_data = []
                    building_names = []

                    for building_id in top_buildings:
                        building_values = filtered_df[filtered_df['building_place_id'] == building_id][col].dropna()
                        if len(building_values) > 0:
                            building_data.append(building_values)
                            # Truncate building ID for display
                            short_id = building_id[-8:] if len(building_id) > 8 else building_id
                            building_names.append(f"BID: {short_id}")

                    if building_data:
                        # Create a subplot with histogram and box plot
                        fig = make_subplots(
                            rows=2,
                            cols=1,
                            row_heights=[0.65, 0.35],  # Give more space to the box plot
                            vertical_spacing=0.15,  # Increase spacing between plots
                            subplot_titles=(
                                "",  # Remove duplicate title
                                "Top 5 Buildings Comparison"
                            )
                        )

                        # Move the histogram to the subplot
                        histogram_trace = go.Histogram(
                            x=overall_data,
                            name="All Data",
                            opacity=0.7,
                            nbinsx=50,
                            marker_color='rgba(58, 71, 80, 0.6)',
                            hovertemplate=(
                                "Value: %{x}<br>"
                                "Count: %{y}<br>"
                                "<extra></extra>"
                            )
                        )
                        fig.add_trace(histogram_trace, row=1, col=1)

                        # Add box plots for top buildings
                        for i, (data, name) in enumerate(zip(building_data, building_names)):
                            fig.add_trace(
                                go.Box(
                                    y=data,
                                    name=name,
                                    boxpoints='outliers',
                                    marker_color=px.colors.qualitative.Plotly[i % len(px.colors.qualitative.Plotly)],
                                    hovertemplate=(
                                        "Building: %{name}<br>"
                                        "Min: %{min:.6f}<br>"
                                        "Q1: %{q1:.6f}<br>"
                                        "Median: %{median:.6f}<br>"
                                        "Q3: %{q3:.6f}<br>"
                                        "Max: %{max:.6f}<br>"
                                        "<extra></extra>"
                                    )
                                ),
                                row=2, col=1
                            )

                        # Only add statistical elements if we have enough data
                        if len(overall_data) > 1:
                            # Add mean and median lines to the histogram subplot
                            fig.add_vline(
                                x=overall_stats['mean'],
                                line_dash="dash",
                                line_color="red",
                                annotation_text="Mean",
                                annotation_position="top right",
                                row=1, col=1
                            )

                            fig.add_vline(
                                x=overall_stats['50%'],
                                line_dash="dash",
                                line_color="green",
                                annotation_text="Median",
                                annotation_position="top left",
                                row=1, col=1
                            )

                        # Add the statistics annotation with improved positioning and formatting
                        fig.add_annotation(
                            xref="x domain",
                            yref="y domain",
                            x=0.01,  # Position on the left side
                            y=0.99,  # Position at the top
                            text=stats_text,
                            showarrow=False,
                            align="left",
                            bgcolor="rgba(255, 255, 255, 0.95)",
                            bordercolor="rgba(0, 0, 0, 0.1)",
                            borderwidth=1,
                            font=dict(size=11, family="Arial"),
                            xanchor="left",
                            yanchor="top",
                            width=180,  # Fixed width for better formatting
                            height=150,  # Fixed height for better formatting
                            opacity=0.9,
                            row=1, col=1
                        )

                # Update layout for clean, professional appearance with improved spacing
                fig.update_layout(
                    title=dict(
                        text=f"{prefix}Distribution of {col}{filter_title}",
                        x=0.5,
                        xanchor='center',
                        y=0.95,  # Move title down slightly
                        font=dict(size=16)  # Slightly larger title font
                    ),
                    showlegend=True,
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.12,  # Move legend higher above the plot
                        xanchor="center",
                        x=0.5,
                        bgcolor='rgba(255,255,255,0.8)',  # Semi-transparent background
                        bordercolor='rgba(0,0,0,0.1)',
                        borderwidth=1
                    ),
                    # Simplified hover label
                    hoverlabel=dict(
                        bgcolor="white",
                        font_size=12
                    ),
                    # Responsive sizing with increased height for better spacing
                    autosize=True,
                    height=650,  # Increased height for better spacing
                    margin=dict(l=50, r=50, t=100, b=50, pad=10),  # Increased margins
                    # Improved performance settings
                    hovermode="closest"
                )

                # Add axis labels
                if 'latitude' in col:
                    x_title = "Latitude"
                elif 'longitude' in col:
                    x_title = "Longitude"
                else:
                    x_title = col

                # Improve axis labels with better formatting
                fig.update_xaxes(
                    title_text=x_title,
                    title_font=dict(size=13),
                    tickfont=dict(size=11),
                    showgrid=True,
                    gridcolor='rgba(0,0,0,0.05)'
                )

                fig.update_yaxes(
                    title_text="Frequency",
                    title_font=dict(size=13),
                    tickfont=dict(size=11),
                    showgrid=True,
                    gridcolor='rgba(0,0,0,0.05)',
                    row=1, col=1
                )

                if 'building_place_id' in filtered_df.columns and building_data and not (entity_type == 'building' and filter_entity):
                    fig.update_yaxes(
                        title_text="Value",
                        title_font=dict(size=13),
                        tickfont=dict(size=11),
                        showgrid=True,
                        gridcolor='rgba(0,0,0,0.05)',
                        row=2, col=1
                    )

                    # Improve subplot title formatting
                    fig.update_annotations(
                        font=dict(size=14, color='#505050'),
                        selector=dict(text="Top 5 Buildings Comparison")
                    )

                # Add enhanced operational explanation with more valuable insights
                if 'latitude' in col or 'longitude' in col:
                    explanation_type = "Latitude" if 'latitude' in col else "Longitude"
                    scan_type = "Entry" if "enter" in col else "Centroid"

                    if "enter" in col:
                        explanation = f"""
                        <b>Entry Point {explanation_type} Analysis:</b><br>
                        This visualization shows the distribution of entry point {explanation_type.lower()} coordinates across the dataset{filter_title}.<br><br>
                        <b>Key Operational Insights:</b><br>
                        1. <b>Route Planning:</b> The concentration of entry points helps optimize delivery routes<br>
                        2. <b>Access Analysis:</b> Patterns reveal how drivers access buildings/complexes<br>
                        3. <b>Outliers:</b> Points beyond the orange lines may indicate incorrect scan locations or special access points<br>
                        4. <b>Building Comparison:</b> The box plots show entry point variations across top buildings<br><br>
                        <b>Operational Applications:</b><br>
                        - <b>Resource Allocation:</b> Position resources based on entry point clusters<br>
                        - <b>Anomaly Detection:</b> Identify buildings with unusual entry patterns<br>
                        - <b>Geographic Coverage:</b> Analyze the spatial distribution of service areas<br>
                        - <b>Efficiency Improvement:</b> Optimize delivery processes based on entry point patterns
                        """
                    elif "centroid" in col:
                        explanation = f"""
                        <b>Centroid {explanation_type} Analysis:</b><br>
                        This visualization shows the distribution of building/complex centroid {explanation_type.lower()} coordinates across the dataset{filter_title}.<br><br>
                        <b>Key Operational Insights:</b><br>
                        1. <b>Service Area Planning:</b> Centroids define optimal service boundaries<br>
                        2. <b>Coverage Analysis:</b> Identify gaps or overlaps in service coverage<br>
                        3. <b>Density Mapping:</b> Understand concentration of buildings for capacity planning<br>
                        4. <b>Building Comparison:</b> The box plots show centroid variations across top buildings<br><br>
                        <b>Operational Applications:</b><br>
                        - <b>Territory Planning:</b> Define service territories based on centroid clusters<br>
                        - <b>Distance Calculation:</b> Measure distances between centroids for logistics planning<br>
                        - <b>Coverage Assessment:</b> Ensure complete geographic coverage of service areas<br>
                        - <b>Resource Optimization:</b> Position resources strategically based on centroid distribution
                        """
                    else:
                        explanation = f"""
                        <b>Operational {explanation_type} Distribution Analysis:</b><br>
                        This visualization shows the distribution of {scan_type} {explanation_type.lower()} coordinates across the dataset{filter_title}.<br><br>
                        <b>Key Operational Insights:</b><br>
                        1. <b>Central Tendency:</b> The mean (red) and median (green) lines show the center of operations<br>
                        2. <b>Outliers:</b> Points beyond the orange lines may indicate scanning errors or unusual locations<br>
                        3. <b>Building Comparison:</b> The box plots show how {explanation_type.lower()} values vary across top buildings<br><br>
                        <b>Operational Applications:</b><br>
                        - <b>Pattern Analysis:</b> Identify geographic clusters and patterns<br>
                        - <b>Error Detection:</b> Find potential geolocation errors in the scanning process<br>
                        - <b>Coverage Assessment:</b> Understand the geographic spread of operations<br>
                        - <b>Performance Analysis:</b> Compare actual locations with expected building locations
                        """
                else:
                    explanation = f"""
                    <b>Distribution Analysis:</b><br>
                    This visualization shows the distribution of {col} values across the dataset{filter_title}.<br><br>
                    <b>Key Operational Insights:</b><br>
                    1. <b>Central Tendency:</b> The mean (red) and median (green) lines show the typical values<br>
                    2. <b>Outliers:</b> Points beyond the orange lines may indicate unusual patterns<br>
                    3. <b>Building Comparison:</b> The box plots show how values vary across top buildings<br><br>
                    <b>Operational Applications:</b><br>
                    - Identify buildings with unusual patterns that may need investigation<br>
                    - Detect potential data anomalies<br>
                    - Understand the typical range of values for operational planning
                    """

                self.add_explanation(fig, explanation)
                self.add_timestamp(fig)
                figs.append(fig)

            logger.info(f"Successfully created {len(figs)} operationally focused histograms")
            return {'figures': figs}

        except Exception as e:
            logger.error(f"Error creating histograms: {str(e)}")
            raise

    def create_enhanced_scatter_plots(self, df: pd.DataFrame, is_cleaned: bool = False, global_filter: dict = None) -> Dict[str, Any]:
        """
        Create enhanced scatter plots with unit-level analysis.

        Args:
            df (pd.DataFrame): Input DataFrame
            is_cleaned (bool): Flag indicating if data is cleaned
            global_filter (dict, optional): Global filter from treemap selection

        Returns:
            Dict[str, Any]: Dictionary containing generated figures
        """
        try:
            logger.info("Creating enhanced scatter plots with unit-level analysis")
            prefix = "Cleaned " if is_cleaned else "Original "

            df_copy = df.copy()
            self._initialize_unit_colors(df_copy)

            # Apply global filter if provided (from treemap)
            filter_title = ""
            if global_filter:
                df_copy, filter_title, entity_type, entity_id = self.apply_global_filter(df_copy, global_filter)

                if df_copy.empty:
                    logger.warning(f"No data found for {entity_type} {entity_id}")
                    return {'figures': [], 'message': f"No data found for {entity_type} {entity_id}"}

            # Ensure proper datetime handling
            if 'date' in df_copy.columns:
                df_copy['date'] = pd.to_datetime(df_copy['date'])

            figs = []

            # 1. Geographic Distribution Plot
            if all(col in df_copy.columns for col in ['enter_latitude', 'enter_longitude', 'unit_place_id']):
                fig = px.scatter_mapbox(
                    df_copy,
                    lat='enter_latitude',
                    lon='enter_longitude',
                    color='unit_place_id',
                    hover_data=['unit_place_id', 'date'],
                    color_discrete_map=self.unit_colors,
                    title=f"{prefix}Geographic Distribution of Units"
                )

                fig.update_layout(
                    mapbox=dict(
                        style="carto-positron",
                        zoom=10,
                        center=dict(
                            lat=df_copy['enter_latitude'].mean(),
                            lon=df_copy['enter_longitude'].mean()
                        )
                    ),
                    hoverlabel=dict(
                        bgcolor="white",
                        font_size=12,
                        font_family="Arial"
                    )
                )

                explanation = """
                <b>Geographic Distribution Analysis:</b><br>
                Visualizes the spatial distribution of unit activities.<br><br>
                <b>Key Features:</b><br>
                - Each point represents an activity location<br>
                - Colors distinguish different units<br>
                - Hover for detailed information<br><br>
                <b>Insights:</b><br>
                - Identify geographic clusters<br>
                - Analyze unit coverage areas<br>
                - Detect spatial patterns
                """
                self.add_explanation(fig, explanation)
                self.add_timestamp(fig)
                figs.append(fig)

            # 2. Temporal Analysis Plot
            if 'date' in df_copy.columns:
                daily_activity = df_copy.groupby(['date', 'unit_place_id']).size().reset_index(name='count')
                fig = px.line(
                    daily_activity,
                    x='date',
                    y='count',
                    color='unit_place_id',
                    color_discrete_map=self.unit_colors,
                    title=f"{prefix}Daily Activity Patterns by Unit"
                )

                fig.update_layout(
                    xaxis_title="Date",
                    yaxis_title="Activity Count",
                    hovermode='x unified'
                )

                explanation = """
                <b>Temporal Pattern Analysis:</b><br>
                Shows activity patterns over time for each unit.<br><br>
                <b>Key Features:</b><br>
                - Daily activity counts by unit<br>
                - Trend visualization<br>
                - Interactive time selection<br><br>
                <b>Insights:</b><br>
                - Identify peak activity periods<br>
                - Compare unit workloads<br>
                - Detect seasonal patterns
                """
                self.add_explanation(fig, explanation)
                self.add_timestamp(fig)
                figs.append(fig)

            # 3. 3D Spatial-Temporal Plot
            if all(col in df_copy.columns for col in ['enter_longitude', 'enter_latitude', 'date', 'unit_place_id']):
                # Create normalized dates for better visualization
                date_min = df_copy['date'].min()
                date_max = df_copy['date'].max()
                df_copy['date_normalized'] = (df_copy['date'] - date_min) / (date_max - date_min)

                fig = px.scatter_3d(
                    df_copy,
                    x='enter_longitude',
                    y='enter_latitude',
                    z='date_normalized',
                    color='unit_place_id',
                    color_discrete_map=self.unit_colors,
                    title=f"{prefix}3D Spatial-Temporal Distribution",
                    custom_data=['date']  # Include actual dates for hover information
                )

                # Generate date ticks for z-axis
                date_range = pd.date_range(date_min, date_max, periods=6)
                date_labels = [d.strftime('%Y-%m-%d') for d in date_range]

                fig.update_layout(
                    scene=dict(
                        xaxis_title="Longitude",
                        yaxis_title="Latitude",
                        zaxis=dict(
                            title="Time",
                            ticktext=date_labels,
                            tickvals=np.linspace(0, 1, 6),
                            tickmode='array'
                        )
                    ),
                    hoverlabel=dict(
                        bgcolor="white",
                        font_size=12,
                        font_family="Arial"
                    )
                )

                # Update hover template to show actual dates
                fig.update_traces(
                    hovertemplate=(
                        "Longitude: %{x:.6f}<br>" +
                        "Latitude: %{y:.6f}<br>" +
                        "Date: %{customdata[0]|%Y-%m-%d %H:%M:%S}<br>" +
                        "Unit: %{color}<br>" +
                        "<extra></extra>"
                    )
                )

                explanation = """
                <b>3D Spatial-Temporal Analysis:</b><br>
                Combines geographic and temporal dimensions.<br><br>
                <b>Key Features:</b><br>
                - 3D visualization of activity patterns<br>
                - Interactive rotation and zoom<br>
                - Integrated time dimension<br><br>
                <b>Insights:</b><br>
                - Analyze movement patterns<br>
                - Identify spatial-temporal clusters<br>
                - Detect pattern changes over time
                """
                self.add_explanation(fig, explanation)
                self.add_timestamp(fig)
                figs.append(fig)

            # 4. Activity Heatmap
            if 'date' in df_copy.columns:
                df_copy['hour'] = df_copy['date'].dt.hour
                df_copy['day_of_week'] = df_copy['date'].dt.day_name()

                activity_pivot = pd.pivot_table(
                    df_copy,
                    values='unit_place_id',
                    index='day_of_week',
                    columns='hour',
                    aggfunc='count',
                    fill_value=0
                )

                fig = px.imshow(
                    activity_pivot,
                    title=f"{prefix}Activity Heatmap by Hour and Day",
                    color_continuous_scale="Viridis"
                )

                fig.update_layout(
                    xaxis_title="Hour of Day",
                    yaxis_title="Day of Week"
                )

                explanation = """
                <b>Activity Heatmap Analysis:</b><br>
                Shows activity patterns across hours and days.<br><br>
                <b>Key Features:</b><br>
                - Hour-by-day activity intensity<br>
                - Color-coded frequency mapping<br>
                - Interactive value display<br><br>
                <b>Insights:</b><br>
                - Identify peak activity times<br>
                - Analyze weekly patterns<br>
                - Optimize resource allocation
                """
                self.add_explanation(fig, explanation)
                self.add_timestamp(fig)
                figs.append(fig)

            logger.info(f"Successfully created {len(figs)} enhanced scatter plots")
            return {'figures': figs}

        except Exception as e:
            logger.error(f"Error creating enhanced scatter plots: {str(e)}")
            raise

    def create_enhanced_correlation_heatmap(self, df: pd.DataFrame, is_cleaned: bool = False, global_filter: dict = None) -> Dict[str, Any]:
        """
        Create enhanced correlation analysis with unit-level insights.

        Args:
            df (pd.DataFrame): Input DataFrame
            is_cleaned (bool): Flag indicating if data is cleaned
            global_filter (dict, optional): Global filter from treemap selection

        Returns:
            Dict[str, Any]: Dictionary containing correlation analysis results
        """
        try:
            logger.info("Creating enhanced correlation analysis")

            # Apply global filter if provided (from treemap)
            filter_title = ""
            if global_filter:
                df, filter_title, entity_type, entity_id = self.apply_global_filter(df, global_filter)

                if df.empty:
                    logger.warning(f"No data found for {entity_type} {entity_id}")
                    return {'figures': [], 'message': f"No data found for {entity_type} {entity_id}"}

            numeric_cols = df.select_dtypes(include=[np.number]).columns
            corr_matrix = df[numeric_cols].corr()
            p_values = self._calculate_correlation_p_values(df[numeric_cols])

            prefix = "Cleaned " if is_cleaned else "Original "
            figs = []

            # 1. Correlation Heatmap
            heatmap = go.Figure(data=go.Heatmap(
                z=corr_matrix.values,
                x=corr_matrix.columns,
                y=corr_matrix.columns,
                hoverongaps=False,
                colorscale='RdBu',
                zmin=-1,
                zmax=1
            ))

            heatmap.update_layout(
                title=f"{prefix}Correlation Heatmap",
                height=650,
                autosize=True,
                margin=dict(l=80, r=80, t=100, b=80),
                font=dict(size=12)
            )

            # Add hover text with correlation values and p-values
            hovertext = [[
                f"Variables: {corr_matrix.columns[i]} vs {corr_matrix.columns[j]}<br>" +
                f"Correlation: {corr_matrix.iloc[i, j]:.3f}<br>" +
                f"P-value: {p_values.iloc[i, j]:.4f}"
                for j in range(len(corr_matrix.columns))
            ] for i in range(len(corr_matrix.columns))]

            heatmap.update_traces(hovertext=hovertext, hoverinfo="text")

            explanation = """
            <b>Correlation Heatmap Analysis:</b><br>
            Shows relationships between numerical variables.<br><br>
            <b>Key Features:</b><br>
            - Blue: Negative correlations<br>
            - Red: Positive correlations<br>
            - Intensity indicates correlation strength<br><br>
            <b>Interpretation:</b><br>
            - Strong correlations: |r| > 0.7<br>
            - Moderate correlations: 0.3 < |r| < 0.7<br>
            - Weak correlations: |r| < 0.3
            """
            self.add_explanation(heatmap, explanation)
            self.add_timestamp(heatmap)
            figs.append(heatmap)

            # 2. Network Graph of Strong Correlations
            network_fig = self._create_correlation_network(corr_matrix, threshold=0.5)
            figs.append(network_fig)

            # 3. Scatter Matrix of Highly Correlated Variables
            top_corr_vars = self._get_top_correlations(corr_matrix, n=5)
            if top_corr_vars:
                plot_df = self._prepare_correlation_data(df, top_corr_vars)

                # Define a custom color palette
                # Define a custom color palette for the scatter matrix
                custom_colors = px.colors.qualitative.Prism  # Vibrant color palette for distinguishing units

                scatter_matrix = px.scatter_matrix(
                    plot_df,
                    dimensions=top_corr_vars,
                    color='unit_place_id' if 'unit_place_id' in plot_df.columns else None,
                    color_discrete_sequence=custom_colors,
                    title=f"{prefix}Scatter Matrix of Top Correlated Variables",
                    labels={col: col.replace('_', ' ').title() for col in top_corr_vars}
                )

                # Customize the appearance further
                scatter_matrix.update_traces(diagonal_visible=False, showupperhalf=False)
                scatter_matrix.update_layout(
                    height=650,
                    autosize=True,
                    legend_title_text='Unit ID',
                    coloraxis_showscale=False,  # Hide color scale as we're using discrete colors
                    margin=dict(l=60, r=60, t=80, b=60)
                )

                explanation = """
                <b>Scatter Matrix Analysis:</b><br>
                Detailed view of relationships between top correlated variables.<br><br>
                <b>Key Features:</b><br>
                - Pairwise scatter plots<br>
                - Distribution histograms<br>
                - Color-coded by unit<br><br>
                <b>Usage:</b><br>
                - Identify non-linear relationships<br>
                - Detect clusters or patterns<br>
                - Analyze unit-specific trends
                """
                self.add_explanation(scatter_matrix, explanation)
                self.add_timestamp(scatter_matrix)
                figs.append(scatter_matrix)

            logger.info("Correlation analysis completed successfully")
            return {
                'figures': figs,
                'correlation_matrix': corr_matrix.to_dict(),
                'p_values': p_values.to_dict()
            }

        except Exception as e:
            logger.error(f"Error in correlation analysis: {str(e)}")
            raise

    def _prepare_correlation_data(self, df: pd.DataFrame, corr_vars: List[str]) -> pd.DataFrame:
        """Prepare data for correlation visualization"""
        plot_df = df[corr_vars].copy()
        if 'unit_place_id' in df.columns:
            plot_df['unit_place_id'] = df['unit_place_id'].astype(str)  # Ensure categorical
        return plot_df


    def _create_correlation_network(self, corr_matrix: pd.DataFrame, threshold: float = 0.5) -> go.Figure:
        """
        Create a network visualization of strong correlations.

        Args:
            corr_matrix (pd.DataFrame): Correlation matrix
            threshold (float): Correlation strength threshold

        Returns:
            go.Figure: Network visualization
        """
        try:
            G = nx.Graph()

            # Add nodes and edges for strong correlations
            for i in range(len(corr_matrix.columns)):
                for j in range(i + 1, len(corr_matrix.columns)):
                    if abs(corr_matrix.iloc[i, j]) >= threshold:
                        G.add_edge(
                            corr_matrix.columns[i],
                            corr_matrix.columns[j],
                            weight=abs(corr_matrix.iloc[i, j])
                        )

            # Use spring layout for node positions
            pos = nx.spring_layout(G)

            # Create network visualization
            edge_trace = go.Scatter(
                x=[],
                y=[],
                line=dict(width=0.5, color='#888'),
                hoverinfo='none',
                mode='lines'
            )

            node_trace = go.Scatter(
                x=[],
                y=[],
                text=[],
                mode='markers+text',
                hoverinfo='text',
                marker=dict(
                    showscale=True,
                    colorscale='YlOrRd',
                    size=20,
                    colorbar=dict(
                        thickness=15,
                        title=dict(
                            text='Node Connections',
                            side='right'
                        ),
                        xanchor='left'
                    )
                )
            )

            # Add edges to visualization
            for edge in G.edges():
                x0, y0 = pos[edge[0]]
                x1, y1 = pos[edge[1]]
                edge_trace['x'] += (x0, x1, None)
                edge_trace['y'] += (y0, y1, None)

            # Add nodes to visualization
            node_x = []
            node_y = []
            node_text = []
            node_adjacencies = []

            for node in G.nodes():
                x, y = pos[node]
                node_x.append(x)
                node_y.append(y)
                node_text.append(node)
                node_adjacencies.append(len(list(G.neighbors(node))))

            node_trace['x'] = node_x
            node_trace['y'] = node_y
            node_trace['text'] = node_text
            node_trace['marker']['color'] = node_adjacencies

            # Create figure
            fig = go.Figure(
                data=[edge_trace, node_trace],
                layout=go.Layout(
                    title='Correlation Network',
                    showlegend=False,
                    hovermode='closest',
                    margin=dict(b=20, l=5, r=5, t=40),
                    height=650,
                    autosize=True
                )
            )

            explanation = """
            <b>Correlation Network Analysis:</b><br>
            Visualizes relationships between variables as a network.<br><br>
            <b>Key Features:</b><br>
            - Nodes: Variables<br>
            - Edges: Strong correlations<br>
            - Node color: Number of connections<br><br>
            <b>Interpretation:</b><br>
            - Larger nodes: More correlations<br>
            - Connected nodes: Related variables<br>
            - Clusters: Related variable groups
            """
            self.add_explanation(fig, explanation)
            self.add_timestamp(fig)

            return fig

        except Exception as e:
            logger.error(f"Error creating correlation network: {str(e)}")
            raise

    def _calculate_correlation_p_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate p-values for correlation coefficients.

        Args:
            df (pd.DataFrame): Input DataFrame with numeric columns

        Returns:
            pd.DataFrame: Matrix of p-values
        """
        try:
            logger.info("Calculating correlation p-values")
            df = df.dropna()
            cols = df.columns
            p_values = pd.DataFrame(np.zeros((len(cols), len(cols))), columns=cols, index=cols)

            for i in range(len(cols)):
                for j in range(i, len(cols)):
                    if i != j:
                        _, p_value = stats.pearsonr(df[cols[i]], df[cols[j]])  # Correlation value not used here
                        p_values.iloc[i, j] = p_value
                        p_values.iloc[j, i] = p_value
                    else:
                        p_values.iloc[i, i] = 1.0

            return p_values

        except Exception as e:
            logger.error(f"Error calculating correlation p-values: {str(e)}")
            raise

    def _get_top_correlations(self, corr_matrix: pd.DataFrame, n: int = 5) -> List[str]:
        """
        Get the top correlated variables.

        Args:
            corr_matrix (pd.DataFrame): Correlation matrix
            n (int): Number of top correlations to return

        Returns:
            List[str]: List of variable names
        """
        try:
            logger.info(f"Getting top {n} correlations")
            # Get upper triangle of correlation matrix
            upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
            # Sort by absolute correlation value
            top_corr = upper.abs().unstack().sort_values(kind="quicksort", ascending=False)[:n]
            # Get unique variables involved in top correlations
            unique_vars = set(top_corr.index.get_level_values(0)) | set(top_corr.index.get_level_values(1))
            return list(unique_vars)

        except Exception as e:
            logger.error(f"Error getting top correlations: {str(e)}")
            raise

    def create_data_quality_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Create a comprehensive data quality report with enhanced visualizations and metrics.

        Args:
            df (pd.DataFrame): Input DataFrame

        Returns:
            Dict[str, Any]: Dictionary containing quality report figures and metrics
        """
        try:
            logger.info("Creating enhanced data quality report")

            if df.empty:
                raise ValueError("Empty DataFrame provided")

            figs = []

            # 1. Missing Data Heatmap (existing implementation)
            missing_data = df.isnull()
            fig_heatmap = px.imshow(
                missing_data.T,
                aspect="auto",
                color_continuous_scale='RdYlBu_r',
                title="Missing Data Pattern Analysis"
            )
            fig_heatmap.update_layout(
                xaxis_title="Row Index",
                yaxis_title="Column",
                height=400
            )
            self.add_explanation(fig_heatmap, "Visualizes patterns of missing data across the dataset. Each column represents a feature, and each row represents a data point. Blue cells indicate missing values, while red cells indicate present values.")
            figs.append(fig_heatmap)

            # 2. Missing Data Summary Bar Chart (enhanced)
            missing_pct = (df.isnull().sum() / len(df) * 100).sort_values(ascending=False)
            fig_bar = px.bar(
                x=missing_pct.index,
                y=missing_pct.values,
                title="Percentage of Missing Values by Column",
                labels={'x': 'Column', 'y': 'Missing Percentage'},
                color=missing_pct.values,
                color_continuous_scale='Viridis'
            )
            fig_bar.update_layout(coloraxis_showscale=False)
            self.add_explanation(fig_bar, "Shows percentage of missing values in each column, color-coded by severity. Higher percentages indicate more missing data in that column.", title="Percentage of Missing Values by Column")
            figs.append(fig_bar)

            # 3. Data Type Distribution
            dtype_counts = df.dtypes.value_counts()
            fig_dtypes = px.pie(
                values=dtype_counts.values,
                names=dtype_counts.index.astype(str),
                title="Data Type Distribution"
            )
            self.add_explanation(fig_dtypes, "Displays the distribution of data types across columns. This helps identify the balance between numerical, categorical, and other data types in the dataset.", title="Data Type Distribution")
            figs.append(fig_dtypes)

            # 4. Numerical Columns Distribution
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                fig_dist = ff.create_distplot(
                    [df[col].dropna()],
                    [col],
                    show_hist=True,
                    show_curve=True
                )
                fig_dist.update_layout(title=f"Distribution of {col}")
                self.add_explanation(fig_dist, f"Shows the distribution of values for the {col} column. The histogram shows the frequency of values, while the curve shows the probability density function.", title=f"Distribution of {col}")
                figs.append(fig_dist)

            # 5. Correlation Heatmap for Numerical Columns
            corr_matrix = df[numeric_cols].corr()
            fig_corr = px.imshow(
                corr_matrix,
                color_continuous_scale='RdBu_r',
                title="Correlation Heatmap for Numerical Columns"
            )
            self.add_explanation(fig_corr, "Displays correlations between numerical columns. Red indicates positive correlation, blue indicates negative correlation, and the intensity of the color indicates the strength of the correlation.", title="Correlation Heatmap for Numerical Columns")
            figs.append(fig_corr)

            # 6. Unit Completeness Analysis (if applicable)
            if 'unit_place_id' in df.columns:
                unit_completeness = df.groupby('unit_place_id').apply(
                    lambda x: x.isnull().sum() / len(x) * 100
                ).reset_index()
                fig_unit = px.box(
                    unit_completeness,
                    points="all",
                    title="Data Completeness Distribution by Unit"
                )
                self.add_explanation(fig_unit, "Shows distribution of missing data across units. Each point represents a unit, and the box plot shows the statistical distribution of missing data percentages across all units.", title="Data Completeness Distribution by Unit")
                figs.append(fig_unit)

            # Calculate additional quality metrics
            quality_metrics = {
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'duplicate_rows': df.duplicated().sum(),
                'completeness_score': (1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
                'data_types': df.dtypes.to_dict(),
                'memory_usage': df.memory_usage(deep=True).sum() / 1024**2  # in MB
            }

            # Add statistical summaries for numerical columns
            for col in numeric_cols:
                quality_metrics[f'{col}_stats'] = df[col].describe().to_dict()

            logger.info("Data quality report created successfully")
            return {
                'figures': figs,
                'metrics': quality_metrics
            }

        except Exception as e:
            logger.error(f"Error in data quality report: {str(e)}")
            raise

    def analyze_missing_delivery_scans(self, df: pd.DataFrame, global_filter: dict = None) -> Dict[str, Any]:
        """
        Create interactive visualizations to analyze missing delivery scans.

        This method identifies patterns in missing delivery scans and provides
        actionable insights through multiple interactive visualizations.

        Args:
            df (pd.DataFrame): Input DataFrame
            global_filter (dict, optional): Global filter from treemap selection

        Returns:
            Dict[str, Any]: Dictionary containing generated figures and analysis metrics
        """
        try:
            logger.info("Creating missing delivery scan analysis visualizations")

            # Make a copy of the dataframe to avoid modifying the original
            df_copy = df.copy()

            # Apply global filter if provided (from treemap)
            filter_title = ""
            if global_filter:
                df_copy, filter_title, entity_type, entity_id = self.apply_global_filter(df_copy, global_filter)

                if df_copy.empty:
                    logger.warning(f"No data found for {entity_type} {entity_id}")
                    return {'figures': [], 'message': f"No data found for {entity_type} {entity_id}"}

            # Ensure proper datetime handling
            if 'date' in df_copy.columns:
                df_copy['date'] = pd.to_datetime(df_copy['date'])

            # Create flags for missing scan data
            df_copy['has_entry_scan'] = df_copy[['enter_latitude', 'enter_longitude']].notna().all(axis=1)
            df_copy['has_exit_scan'] = df_copy[['exit_latitude', 'exit_longitude']].notna().all(axis=1)
            df_copy['has_delivery_scan'] = df_copy[['scan_latitude', 'scan_longitude']].notna().all(axis=1)

            # Calculate missing scan patterns
            df_copy['scan_pattern'] = df_copy.apply(
                lambda row: f"Entry: {'✓' if row['has_entry_scan'] else '✗'}, "
                           f"Exit: {'✓' if row['has_exit_scan'] else '✗'}, "
                           f"Delivery: {'✓' if row['has_delivery_scan'] else '✗'}",
                axis=1
            )

            # Add time-based features for pattern analysis
            if 'date' in df_copy.columns:
                df_copy['hour'] = df_copy['date'].dt.hour
                df_copy['day_of_week'] = df_copy['date'].dt.day_name()
                df_copy['day'] = df_copy['date'].dt.day
                df_copy['month'] = df_copy['date'].dt.month
                df_copy['year'] = df_copy['date'].dt.year
                df_copy['week_of_year'] = df_copy['date'].dt.isocalendar().week

            # Initialize figures list and metrics dictionary
            figs = []
            metrics = {}

            # Calculate overall metrics
            total_records = len(df_copy)
            missing_delivery = (~df_copy['has_delivery_scan']).sum()
            missing_entry = (~df_copy['has_entry_scan']).sum()
            missing_exit = (~df_copy['has_exit_scan']).sum()

            metrics.update({
                'total_records': total_records,
                'missing_delivery_count': missing_delivery,
                'missing_delivery_percent': (missing_delivery / total_records * 100) if total_records > 0 else 0,
                'missing_entry_count': missing_entry,
                'missing_entry_percent': (missing_entry / total_records * 100) if total_records > 0 else 0,
                'missing_exit_count': missing_exit,
                'missing_exit_percent': (missing_exit / total_records * 100) if total_records > 0 else 0
            })

            # 1. Missing Scan Patterns Sunburst Chart
            pattern_counts = df_copy.groupby(['has_entry_scan', 'has_exit_scan', 'has_delivery_scan']).size().reset_index(name='count')

            # Create labels for the sunburst chart
            pattern_counts['entry_label'] = pattern_counts['has_entry_scan'].apply(lambda x: 'Entry Scan' if x else 'No Entry Scan')
            pattern_counts['exit_label'] = pattern_counts['has_exit_scan'].apply(lambda x: 'Exit Scan' if x else 'No Exit Scan')
            pattern_counts['delivery_label'] = pattern_counts['has_delivery_scan'].apply(lambda x: 'Delivery Scan' if x else 'No Delivery Scan')

            # Create hierarchical data for sunburst
            sunburst_data = []

            # Add root
            sunburst_data.append({
                'id': 'All Records',
                'parent': '',
                'value': total_records
            })

            # Add entry level
            for entry in [True, False]:
                entry_label = 'Entry Scan' if entry else 'No Entry Scan'
                entry_count = pattern_counts[pattern_counts['has_entry_scan'] == entry]['count'].sum()
                if entry_count > 0:
                    sunburst_data.append({
                        'id': entry_label,
                        'parent': 'All Records',
                        'value': entry_count
                    })

                    # Add exit level
                    for exit_scan in [True, False]:
                        exit_label = f"{entry_label} + {'Exit Scan' if exit_scan else 'No Exit Scan'}"
                        exit_count = pattern_counts[(pattern_counts['has_entry_scan'] == entry) &
                                                   (pattern_counts['has_exit_scan'] == exit_scan)]['count'].sum()
                        if exit_count > 0:
                            sunburst_data.append({
                                'id': exit_label,
                                'parent': entry_label,
                                'value': exit_count
                            })

                            # Add delivery level
                            for delivery in [True, False]:
                                delivery_label = f"{exit_label} + {'Delivery Scan' if delivery else 'No Delivery Scan'}"
                                delivery_count = pattern_counts[(pattern_counts['has_entry_scan'] == entry) &
                                                              (pattern_counts['has_exit_scan'] == exit_scan) &
                                                              (pattern_counts['has_delivery_scan'] == delivery)]['count'].sum()
                                if delivery_count > 0:
                                    sunburst_data.append({
                                        'id': delivery_label,
                                        'parent': exit_label,
                                        'value': delivery_count
                                    })

            # Create sunburst chart
            sunburst_df = pd.DataFrame(sunburst_data)
            fig_sunburst = px.sunburst(
                sunburst_df,
                ids='id',
                parents='parent',
                values='value',
                title="Missing Scan Patterns Analysis",
                color_discrete_sequence=px.colors.qualitative.Bold,
                branchvalues='total'
            )

            # Add custom hover template
            fig_sunburst.update_traces(
                hovertemplate='<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percentRoot:.1%}<extra></extra>'
            )

            # Add explanation
            explanation = """
            <b>Missing Scan Patterns Analysis:</b><br>
            This visualization shows the hierarchical breakdown of scan patterns.<br><br>
            <b>How to Use:</b><br>
            - Click on segments to drill down into specific patterns<br>
            - Hover for detailed counts and percentages<br>
            - Click the center to go back up the hierarchy<br><br>
            <b>Key Insights:</b><br>
            - Identify common missing scan patterns<br>
            - Quantify the impact of each pattern<br>
            - Detect relationships between different scan types
            """
            self.add_explanation(fig_sunburst, explanation)
            self.add_timestamp(fig_sunburst)
            figs.append(fig_sunburst)

            # 2. Missing Delivery Scans by Time - Heatmap
            if 'date' in df_copy.columns:
                # Create pivot table for day of week vs hour heatmap
                missing_by_time = pd.pivot_table(
                    df_copy[~df_copy['has_delivery_scan']],
                    values='unit_place_id',
                    index='day_of_week',
                    columns='hour',
                    aggfunc='count',
                    fill_value=0
                )

                # Reorder days of week
                day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
                missing_by_time = missing_by_time.reindex(day_order)

                # Create heatmap
                fig_heatmap = px.imshow(
                    missing_by_time,
                    title="Missing Delivery Scans by Day and Hour",
                    labels=dict(x="Hour of Day", y="Day of Week", color="Count"),
                    color_continuous_scale="Reds",
                    aspect="auto"
                )

                # Add custom hover template
                fig_heatmap.update_traces(
                    hovertemplate='<b>%{y}, %{x}:00</b><br>Missing Scans: %{z}<extra></extra>'
                )

                # Add explanation
                explanation = """
                <b>Missing Delivery Scans by Time:</b><br>
                Shows when delivery scans are most commonly missing.<br><br>
                <b>Key Features:</b><br>
                - Day and hour breakdown<br>
                - Color intensity shows frequency<br>
                - Interactive hover details<br><br>
                <b>Operational Insights:</b><br>
                - Identify problematic time periods<br>
                - Target training during high-miss periods<br>
                - Optimize staffing during critical times
                """
                self.add_explanation(fig_heatmap, explanation)
                self.add_timestamp(fig_heatmap)
                figs.append(fig_heatmap)

                # 3. Missing Delivery Scans Trend Over Time
                # Group by date and calculate missing scan percentages
                daily_missing = df_copy.groupby(df_copy['date'].dt.date).apply(
                    lambda x: pd.Series({
                        'total': len(x),
                        'missing_delivery': (~x['has_delivery_scan']).sum(),
                        'missing_entry': (~x['has_entry_scan']).sum(),
                        'missing_exit': (~x['has_exit_scan']).sum()
                    })
                ).reset_index()

                # Calculate percentages
                daily_missing['missing_delivery_pct'] = daily_missing['missing_delivery'] / daily_missing['total'] * 100
                daily_missing['missing_entry_pct'] = daily_missing['missing_entry'] / daily_missing['total'] * 100
                daily_missing['missing_exit_pct'] = daily_missing['missing_exit'] / daily_missing['total'] * 100

                # Create line chart
                fig_trend = go.Figure()

                # Add traces for each scan type
                fig_trend.add_trace(go.Scatter(
                    x=daily_missing['date'],
                    y=daily_missing['missing_delivery_pct'],
                    mode='lines+markers',
                    name='Missing Delivery Scans',
                    line=dict(color='red', width=2),
                    hovertemplate='<b>%{x|%Y-%m-%d}</b><br>Missing: %{y:.1f}%<br>Count: %{customdata[0]}<extra></extra>',
                    customdata=daily_missing[['missing_delivery']]
                ))

                fig_trend.add_trace(go.Scatter(
                    x=daily_missing['date'],
                    y=daily_missing['missing_entry_pct'],
                    mode='lines+markers',
                    name='Missing Entry Scans',
                    line=dict(color='blue', width=2),
                    hovertemplate='<b>%{x|%Y-%m-%d}</b><br>Missing: %{y:.1f}%<br>Count: %{customdata[0]}<extra></extra>',
                    customdata=daily_missing[['missing_entry']]
                ))

                fig_trend.add_trace(go.Scatter(
                    x=daily_missing['date'],
                    y=daily_missing['missing_exit_pct'],
                    mode='lines+markers',
                    name='Missing Exit Scans',
                    line=dict(color='green', width=2),
                    hovertemplate='<b>%{x|%Y-%m-%d}</b><br>Missing: %{y:.1f}%<br>Count: %{customdata[0]}<extra></extra>',
                    customdata=daily_missing[['missing_exit']]
                ))

                # Add moving average for delivery scans
                window_size = min(7, len(daily_missing))
                if window_size > 1:
                    daily_missing['delivery_ma'] = daily_missing['missing_delivery_pct'].rolling(window=window_size).mean()

                    fig_trend.add_trace(go.Scatter(
                        x=daily_missing['date'],
                        y=daily_missing['delivery_ma'],
                        mode='lines',
                        name=f'{window_size}-Day Moving Avg (Delivery)',
                        line=dict(color='rgba(255, 0, 0, 0.5)', width=3, dash='dot'),
                        hovertemplate='<b>%{x|%Y-%m-%d}</b><br>MA: %{y:.1f}%<extra></extra>'
                    ))

                # Update layout
                fig_trend.update_layout(
                    title="Missing Scan Trends Over Time",
                    xaxis_title="Date",
                    yaxis_title="Missing Scans (%)",
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    ),
                    margin=dict(l=40, r=40, t=60, b=40),
                    hovermode="x unified"
                )

                # Add range slider
                fig_trend.update_xaxes(
                    rangeslider_visible=True,
                    rangeselector=dict(
                        buttons=list([
                            dict(count=7, label="1w", step="day", stepmode="backward"),
                            dict(count=1, label="1m", step="month", stepmode="backward"),
                            dict(count=3, label="3m", step="month", stepmode="backward"),
                            dict(step="all")
                        ])
                    )
                )

                # Add explanation
                explanation = """
                <b>Missing Scan Trends Analysis:</b><br>
                Shows how missing scan rates change over time.<br><br>
                <b>Key Features:</b><br>
                - Daily missing scan percentages<br>
                - Comparison across scan types<br>
                - Moving average trend line<br>
                - Interactive date range selection<br><br>
                <b>Operational Insights:</b><br>
                - Identify improving or worsening trends<br>
                - Detect impact of process changes<br>
                - Compare performance across time periods<br>
                - Forecast future performance
                """
                self.add_explanation(fig_trend, explanation)
                self.add_timestamp(fig_trend)
                figs.append(fig_trend)

            # 4. Missing Delivery Scans by Unit/Building - Interactive Bar Chart
            if 'unit_place_id' in df_copy.columns and 'building_place_id' in df_copy.columns:
                # Calculate missing scan rates by unit
                unit_missing = df_copy.groupby('unit_place_id').apply(
                    lambda x: pd.Series({
                        'total': len(x),
                        'missing_delivery': (~x['has_delivery_scan']).sum(),
                        'missing_delivery_pct': (~x['has_delivery_scan']).sum() / len(x) * 100,
                        'building_id': x['building_place_id'].iloc[0]
                    })
                ).reset_index()

                # Sort by missing percentage
                unit_missing = unit_missing.sort_values('missing_delivery_pct', ascending=False)

                # Limit to top 50 units for better visualization
                top_units = unit_missing.head(50)

                # Create interactive bar chart
                fig_units = px.bar(
                    top_units,
                    x='unit_place_id',
                    y='missing_delivery_pct',
                    color='missing_delivery_pct',
                    color_continuous_scale='Reds',
                    hover_data=['total', 'missing_delivery', 'building_id'],
                    labels={
                        'unit_place_id': 'Unit ID',
                        'missing_delivery_pct': 'Missing Delivery Scans (%)',
                        'total': 'Total Records',
                        'missing_delivery': 'Missing Scans',
                        'building_id': 'Building ID'
                    },
                    title="Top Units with Missing Delivery Scans"
                )

                # Update hover template
                fig_units.update_traces(
                    hovertemplate='<b>Unit: %{x}</b><br>Missing: %{y:.1f}%<br>Count: %{customdata[1]}/{%customdata[0]}<br>Building: %{customdata[2]}<extra></extra>'
                )

                # Update layout
                fig_units.update_layout(
                    xaxis_title="Unit ID",
                    yaxis_title="Missing Delivery Scans (%)",
                    xaxis={'categoryorder': 'total descending'},
                    margin=dict(l=40, r=40, t=60, b=120),
                )

                # Rotate x-axis labels for better readability
                fig_units.update_xaxes(tickangle=45)

                # Add explanation
                explanation = """
                <b>Missing Delivery Scans by Unit:</b><br>
                Identifies units with the highest rates of missing delivery scans.<br><br>
                <b>Key Features:</b><br>
                - Units ranked by missing scan percentage<br>
                - Color intensity indicates severity<br>
                - Hover for detailed counts and building info<br><br>
                <b>Operational Applications:</b><br>
                - Target specific units for improvement<br>
                - Identify problematic buildings<br>
                - Prioritize training and process improvements<br>
                - Set unit-specific performance goals
                """
                self.add_explanation(fig_units, explanation)
                self.add_timestamp(fig_units)
                figs.append(fig_units)

                # 5. Building-Level Analysis - Bubble Chart
                # Calculate missing scan rates by building
                building_missing = df_copy.groupby('building_place_id').apply(
                    lambda x: pd.Series({
                        'total': len(x),
                        'missing_delivery': (~x['has_delivery_scan']).sum(),
                        'missing_delivery_pct': (~x['has_delivery_scan']).sum() / len(x) * 100,
                        'units': x['unit_place_id'].nunique(),
                        'avg_lat': x['enter_latitude'].mean(),
                        'avg_lon': x['enter_longitude'].mean()
                    })
                ).reset_index()

                # Filter out buildings with very few records for statistical significance
                building_missing = building_missing[building_missing['total'] >= 5]

                # Create bubble chart
                fig_buildings = px.scatter(
                    building_missing,
                    x='total',
                    y='missing_delivery_pct',
                    size='units',
                    color='missing_delivery_pct',
                    color_continuous_scale='Reds',
                    hover_name='building_place_id',
                    hover_data=['missing_delivery', 'units'],
                    labels={
                        'total': 'Total Records',
                        'missing_delivery_pct': 'Missing Delivery Scans (%)',
                        'units': 'Number of Units',
                        'missing_delivery': 'Missing Scans'
                    },
                    title="Building Analysis: Missing Delivery Scans",
                    size_max=50
                )

                # Update hover template
                fig_buildings.update_traces(
                    hovertemplate='<b>Building: %{hovertext}</b><br>Missing: %{y:.1f}%<br>Records: %{x}<br>Units: %{customdata[1]}<br>Missing Count: %{customdata[0]}<extra></extra>'
                )

                # Add reference lines
                overall_missing_pct = metrics['missing_delivery_percent']
                fig_buildings.add_hline(
                    y=overall_missing_pct,
                    line_dash="dash",
                    line_color="red",
                    annotation_text=f"Overall Average: {overall_missing_pct:.1f}%",
                    annotation_position="top right"
                )

                # Update layout
                fig_buildings.update_layout(
                    xaxis_title="Total Records",
                    yaxis_title="Missing Delivery Scans (%)",
                    margin=dict(l=40, r=40, t=60, b=40),
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )

                # Add explanation
                explanation = """
                <b>Building-Level Missing Scan Analysis:</b><br>
                Compares buildings based on missing delivery scan rates.<br><br>
                <b>Key Features:</b><br>
                - X-axis: Total record count (volume)<br>
                - Y-axis: Missing scan percentage (performance)<br>
                - Bubble size: Number of units in building<br>
                - Color: Missing scan percentage (intensity)<br><br>
                <b>How to Interpret:</b><br>
                - Upper right: High-volume buildings with poor performance<br>
                - Upper left: Low-volume buildings with poor performance<br>
                - Lower right: High-volume buildings with good performance<br>
                - Lower left: Low-volume buildings with good performance<br><br>
                <b>Operational Strategy:</b><br>
                - Prioritize large bubbles above the average line<br>
                - These represent high-impact improvement opportunities
                """
                self.add_explanation(fig_buildings, explanation)
                self.add_timestamp(fig_buildings)
                figs.append(fig_buildings)

                # 6. Geographic Distribution of Missing Scans
                # Only create map if we have geographic coordinates
                if all(col in building_missing.columns for col in ['avg_lat', 'avg_lon']):
                    # Filter out buildings with missing coordinates
                    map_data = building_missing.dropna(subset=['avg_lat', 'avg_lon'])

                    if not map_data.empty:
                        # Create map visualization
                        fig_map = px.scatter_mapbox(
                            map_data,
                            lat='avg_lat',
                            lon='avg_lon',
                            size='missing_delivery',
                            color='missing_delivery_pct',
                            color_continuous_scale='Reds',
                            hover_name='building_place_id',
                            hover_data=['total', 'missing_delivery', 'missing_delivery_pct', 'units'],
                            zoom=10,
                            mapbox_style="carto-positron",
                            title="Geographic Distribution of Missing Delivery Scans"
                        )

                        # Update hover template
                        fig_map.update_traces(
                            hovertemplate='<b>Building: %{hovertext}</b><br>Missing: %{marker.color:.1f}%<br>Count: %{marker.size}<br>Total Records: %{customdata[0]}<br>Units: %{customdata[3]}<extra></extra>'
                        )

                        # Update layout
                        fig_map.update_layout(
                            margin=dict(l=0, r=0, t=40, b=0),
                            mapbox=dict(
                                center=dict(
                                    lat=map_data['avg_lat'].mean(),
                                    lon=map_data['avg_lon'].mean()
                                )
                            )
                        )

                        # Add explanation
                        explanation = """
                        <b>Geographic Analysis of Missing Scans:</b><br>
                        Shows the spatial distribution of missing delivery scans.<br><br>
                        <b>Key Features:</b><br>
                        - Each bubble represents a building<br>
                        - Size: Number of missing scans<br>
                        - Color: Missing scan percentage<br>
                        - Location: Geographic position<br><br>
                        <b>Operational Insights:</b><br>
                        - Identify geographic clusters of missing scans<br>
                        - Detect regional performance variations<br>
                        - Target specific areas for improvement<br>
                        - Optimize resource allocation by region
                        """
                        self.add_explanation(fig_map, explanation)
                        self.add_timestamp(fig_map)
                        figs.append(fig_map)

            # Return the figures and metrics
            logger.info(f"Successfully created {len(figs)} missing delivery scan analysis visualizations")
            return {
                'figures': figs,
                'metrics': metrics
            }

        except Exception as e:
            logger.error(f"Error analyzing missing delivery scans: {str(e)}")
            raise

    def create_summary_dashboard(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Create a comprehensive summary dashboard with enhanced visualizations and metrics.

        Args:
            df (pd.DataFrame): Input DataFrame

        Returns:
            Dict[str, Any]: Dictionary containing dashboard figures and summary statistics
        """
        try:
            logger.info("Creating enhanced summary dashboard")

            if df.empty:
                raise ValueError("Empty DataFrame provided")

            figs = []

            # Ensure proper datetime handling
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])

            # 1. Enhanced Activity Timeline
            if 'date' in df.columns:
                daily_counts = df.groupby('date').size().reset_index(name='count')
                daily_counts['MA7'] = daily_counts['count'].rolling(window=7).mean()

                fig_timeline = make_subplots(specs=[[{"secondary_y": True}]])
                fig_timeline.add_trace(
                    go.Bar(x=daily_counts['date'], y=daily_counts['count'], name="Daily Count"),
                    secondary_y=False
                )
                fig_timeline.add_trace(
                    go.Scatter(x=daily_counts['date'], y=daily_counts['MA7'],
                               name="7-day Moving Average", line=dict(color='red', width=2)),
                    secondary_y=True
                )
                fig_timeline.update_layout(
                    title="Activity Timeline with 7-day Moving Average",
                    xaxis_title="Date",
                    yaxis_title="Daily Count",
                    yaxis2_title="7-day Moving Average"
                )
                self.add_explanation(fig_timeline, "Shows daily activity counts and 7-day moving average. The bars represent daily counts, while the red line shows the 7-day moving average to highlight trends.", title="Activity Timeline with 7-day Moving Average")
                figs.append(fig_timeline)

            # 2. Enhanced Unit Analysis
            if 'unit_place_id' in df.columns:
                unit_metrics = df.groupby('unit_place_id').agg({
                    'date': ['count', 'nunique'],
                    'enter_latitude': 'count',
                    'exit_latitude': 'count',
                    'scan_latitude': 'count'
                }).reset_index()
                unit_metrics.columns = ['unit_place_id', 'total_activities', 'active_days',
                                        'enter_scans', 'exit_scans', 'delivery_scans']

                fig_unit = px.bar(
                    unit_metrics,
                    x='unit_place_id',
                    y=['enter_scans', 'exit_scans', 'delivery_scans'],
                    title="Comprehensive Unit Performance",
                    barmode='group'
                )
                fig_unit.update_layout(xaxis_title="Unit ID", yaxis_title="Scan Count")
                self.add_explanation(fig_unit, "Displays various scan types for each unit. The grouped bars show the count of entry, exit, and delivery scans for each unit, allowing for easy comparison of unit performance.", title="Comprehensive Unit Performance")
                figs.append(fig_unit)

                # 3. Unit Activity Heatmap
                if 'date' in df.columns:
                    unit_daily = df.groupby(['unit_place_id', df['date'].dt.date]).size().unstack(fill_value=0)
                    fig_heatmap = px.imshow(
                        unit_daily,
                        title="Unit Activity Heatmap",
                        labels=dict(x="Date", y="Unit ID", color="Activity Count"),
                        aspect="auto"
                    )
                    self.add_explanation(fig_heatmap, "Shows activity intensity for each unit over time. The color intensity represents the number of activities for each unit on each day, with darker colors indicating higher activity levels.", title="Unit Activity Heatmap")
                    figs.append(fig_heatmap)

            # 4. Geographical Distribution (if applicable)
            if all(col in df.columns for col in ['enter_latitude', 'enter_longitude']):
                fig_geo = px.scatter_mapbox(
                    df,
                    lat='enter_latitude',
                    lon='enter_longitude',
                    color='unit_place_id' if 'unit_place_id' in df.columns else None,
                    title="Geographical Distribution of Activities",
                    zoom=3,
                    mapbox_style="open-street-map"
                )
                self.add_explanation(fig_geo, "Visualizes the geographical distribution of activities. Each point represents an activity location, color-coded by unit. This visualization helps identify geographic patterns and unit coverage areas.", title="Geographical Distribution of Activities")
                figs.append(fig_geo)

            # Calculate summary statistics
            summary_stats = {
                'total_records': len(df),
                'date_range': f"{df['date'].min()} to {df['date'].max()}" if 'date' in df.columns else None,
                'total_units': df['unit_place_id'].nunique() if 'unit_place_id' in df.columns else None,
                'avg_daily_activity': daily_counts['count'].mean() if 'date' in df.columns else None
            }

            # Add more detailed statistics
            if 'date' in df.columns:
                summary_stats.update({
                    'busiest_day': daily_counts.loc[daily_counts['count'].idxmax(), 'date'].strftime('%Y-%m-%d'),
                    'quietest_day': daily_counts.loc[daily_counts['count'].idxmin(), 'date'].strftime('%Y-%m-%d'),
                    'activity_trend': 'Increasing' if daily_counts['MA7'].iloc[-1] > daily_counts['MA7'].iloc[0] else 'Decreasing'
                })

            if 'unit_place_id' in df.columns:
                summary_stats.update({
                    'most_active_unit': unit_metrics.loc[unit_metrics['total_activities'].idxmax(), 'unit_place_id'],
                    'least_active_unit': unit_metrics.loc[unit_metrics['total_activities'].idxmin(), 'unit_place_id']
                })

            logger.info("Summary dashboard created successfully")
            return {
                'figures': figs,
                'summary_stats': summary_stats
            }

        except Exception as e:
            logger.error(f"Error in summary dashboard: {str(e)}")
            raise

    def create_3d_scatter(self, df: pd.DataFrame, global_filter: dict = None) -> go.Figure:
        """
        Create a 3D scatter plot of the geographic data.

        Args:
            df (pd.DataFrame): Input DataFrame
            global_filter (dict, optional): Global filter from treemap selection

        Returns:
            go.Figure: 3D scatter plot visualization
        """
        try:
            logger.info("Creating 3D scatter plot")

            # Create a copy of the dataframe to avoid modifying the original
            df_copy = df.copy()

            # Apply global filter if provided (from treemap)
            filter_title = ""
            if global_filter:
                df_copy, filter_title, entity_type, entity_id = self.apply_global_filter(df_copy, global_filter)

                if df_copy.empty:
                    logger.warning(f"No data found for {entity_type} {entity_id}")
                    empty_fig = go.Figure()
                    empty_fig.add_annotation(
                        text=f"No data found for {entity_type} {entity_id}",
                        xref="paper", yref="paper",
                        x=0.5, y=0.5,
                        showarrow=False,
                        font=dict(size=20)
                    )
                    return empty_fig

            # Ensure proper datetime handling
            if 'date' in df_copy.columns:
                df_copy['date'] = pd.to_datetime(df_copy['date'])

            # Create a 3D scatter plot of the geographic data
            if all(col in df_copy.columns for col in ['enter_longitude', 'enter_latitude', 'exit_longitude', 'exit_latitude']):
                # Create a figure with subplots
                fig = make_subplots(
                    rows=1, cols=1,
                    specs=[[{'type': 'scatter3d'}]],
                    subplot_titles=["3D Geographic Distribution"]
                )

                # Add enter scan points
                fig.add_trace(
                    go.Scatter3d(
                        x=df_copy['enter_longitude'],
                        y=df_copy['enter_latitude'],
                        z=df_copy['date'].astype(np.int64) // 10**9 if 'date' in df_copy.columns else np.zeros(len(df_copy)),
                        mode='markers',
                        marker=dict(
                            size=5,
                            color='blue',
                            opacity=0.7
                        ),
                        name='Enter Scans',
                        hovertemplate=(
                            "<b>Enter Scan</b><br>" +
                            "Longitude: %{x}<br>" +
                            "Latitude: %{y}<br>" +
                            "Date: %{text}<br>" +
                            "<extra></extra>"
                        ),
                        text=df_copy['date'].dt.strftime('%Y-%m-%d') if 'date' in df_copy.columns else ""
                    )
                )

                # Add exit scan points
                fig.add_trace(
                    go.Scatter3d(
                        x=df_copy['exit_longitude'],
                        y=df_copy['exit_latitude'],
                        z=df_copy['date'].astype(np.int64) // 10**9 if 'date' in df_copy.columns else np.zeros(len(df_copy)),
                        mode='markers',
                        marker=dict(
                            size=5,
                            color='red',
                            opacity=0.7
                        ),
                        name='Exit Scans',
                        hovertemplate=(
                            "<b>Exit Scan</b><br>" +
                            "Longitude: %{x}<br>" +
                            "Latitude: %{y}<br>" +
                            "Date: %{text}<br>" +
                            "<extra></extra>"
                        ),
                        text=df_copy['date'].dt.strftime('%Y-%m-%d') if 'date' in df_copy.columns else ""
                    )
                )

                # Add delivery scan points if available
                if all(col in df_copy.columns for col in ['scan_longitude', 'scan_latitude']):
                    fig.add_trace(
                        go.Scatter3d(
                            x=df_copy['scan_longitude'],
                            y=df_copy['scan_latitude'],
                            z=df_copy['date'].astype(np.int64) // 10**9 if 'date' in df_copy.columns else np.zeros(len(df_copy)),
                            mode='markers',
                            marker=dict(
                                size=5,
                                color='green',
                                opacity=0.7
                            ),
                            name='Delivery Scans',
                            hovertemplate=(
                                "<b>Delivery Scan</b><br>" +
                                "Longitude: %{x}<br>" +
                                "Latitude: %{y}<br>" +
                                "Date: %{text}<br>" +
                                "<extra></extra>"
                            ),
                            text=df_copy['date'].dt.strftime('%Y-%m-%d') if 'date' in df_copy.columns else ""
                        )
                    )

                # Update layout
                fig.update_layout(
                    title=f"3D Geographic Distribution{filter_title}",
                    scene=dict(
                        xaxis_title="Longitude",
                        yaxis_title="Latitude",
                        zaxis_title="Time",
                        aspectmode='auto'
                    ),
                    legend=dict(
                        x=0.01,
                        y=0.99,
                        bordercolor="Black",
                        borderwidth=1
                    ),
                    margin=dict(l=0, r=0, b=0, t=30),
                    height=700
                )

                # Add explanation
                explanation = """
                <b>3D Geographic Distribution:</b><br>
                This visualization shows the spatial distribution of scans in 3D space.<br><br>
                <b>Key Features:</b><br>
                - Blue points: Enter scans<br>
                - Red points: Exit scans<br>
                - Green points: Delivery scans<br>
                - Z-axis: Time dimension<br><br>
                <b>Usage:</b><br>
                - Rotate to view from different angles<br>
                - Zoom in/out for detail<br>
                - Hover for point details<br>
                - Toggle scan types using legend
                """
                self.add_explanation(fig, explanation)
                self.add_timestamp(fig)

                return fig
            else:
                # Create a simple message if required columns are not available
                fig = go.Figure()
                fig.add_annotation(
                    text="Required geographic coordinates not available in the dataset",
                    xref="paper", yref="paper",
                    x=0.5, y=0.5,
                    showarrow=False,
                    font=dict(size=20)
                )
                return fig

        except Exception as e:
            logger.error(f"Error creating 3D scatter plot: {str(e)}")
            logger.error("Full traceback:", exc_info=True)
            # Create an error figure
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating 3D scatter plot: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20, color="red")
            )
            return fig

    def create_animated_time_series(self, df: pd.DataFrame, global_filter: dict = None) -> go.Figure:
        try:
            logger.info("Creating enhanced animated time series")

            # 1. Data Preparation
            df_work = df.copy()

            # Apply global filter if provided (from treemap)
            filter_title = ""
            if global_filter:
                df_work, filter_title, entity_type, entity_id = self.apply_global_filter(df_work, global_filter)

                if df_work.empty:
                    logger.warning(f"No data found for {entity_type} {entity_id}")
                    # Return an empty figure with a message
                    empty_fig = go.Figure()
                    empty_fig.add_annotation(
                        text=f"No data found for {entity_type} {entity_id}",
                        xref="paper", yref="paper",
                        x=0.5, y=0.5,
                        showarrow=False,
                        font=dict(size=20)
                    )
                    return empty_fig

            df_work['date'] = pd.to_datetime(df_work['date'])

            # Create scan type indicators
            df_work['enter_scans'] = df_work.apply(
                lambda row: np.sqrt((row['enter_latitude'])**2 + (row['enter_longitude'])**2)
                if pd.notna(row['enter_latitude']) and pd.notna(row['enter_longitude']) else np.nan, axis=1)
            df_work['exit_scans'] = df_work.apply(
                lambda row: np.sqrt((row['exit_latitude'])**2 + (row['exit_longitude'])**2)
                if pd.notna(row['exit_latitude']) and pd.notna(row['exit_longitude']) else np.nan, axis=1)
            df_work['delivery_scans'] = df_work.apply(
                lambda row: np.sqrt((row['scan_latitude'])**2 + (row['scan_longitude'])**2)
                if pd.notna(row['scan_latitude']) and pd.notna(row['scan_longitude']) else np.nan, axis=1)

            # Group by date and calculate daily metrics
            df_grouped = df_work.groupby(df_work['date'].dt.date).agg({
                'enter_scans': ['count', 'mean', 'std'],
                'exit_scans': ['count', 'mean', 'std'],
                'delivery_scans': ['count', 'mean', 'std']
            }).reset_index()

            # Flatten column names
            df_grouped.columns = ['date'] + [f'{col[0]}_{col[1]}' for col in df_grouped.columns[1:]]

            # Create visualization
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    'Scan Activity Patterns (3D)',
                    'Daily Scan Trends',
                    'Scan Distribution Analysis',
                    'Statistical Summary'
                ),
                specs=[[{"type": "scatter3d"}, {"type": "scatter"}],
                    [{"type": "box"}, {"type": "table"}]],
                vertical_spacing=0.15,
                horizontal_spacing=0.1
            )

            # Panel 1: 3D Scan Activity
            scatter_3d = go.Scatter3d(
                x=df_grouped['enter_scans_count'],
                y=df_grouped['exit_scans_count'],
                z=df_grouped['delivery_scans_count'],
                mode='markers+lines',
                marker=dict(
                    size=6,
                    color=df_grouped.index,
                    colorscale='Viridis',
                    opacity=0.8,
                    showscale=True,
                    colorbar=dict(title="Time Progression")
                ),
                line=dict(width=3, color='rgba(70,130,180,0.6)'),
                name='Daily Scan Activity',
                text=df_grouped['date'],
                hovertemplate=(
                    "<b>Date:</b> %{text}<br>" +
                    "<b>Enter Scans:</b> %{x}<br>" +
                    "<b>Exit Scans:</b> %{y}<br>" +
                    "<b>Delivery Scans:</b> %{z}<br>" +
                    "<extra></extra>"
                )
            )
            fig.add_trace(scatter_3d, row=1, col=1)

            # Panel 2: Daily Trends
            colors = ['rgb(70,130,180)', 'rgb(178,34,34)', 'rgb(50,205,50)']
            for idx, scan_type in enumerate(['enter', 'exit', 'delivery']):
                fig.add_trace(
                    go.Scatter(
                        x=df_grouped['date'],
                        y=df_grouped[f'{scan_type}_scans_count'],
                        name=f'{scan_type.title()} Scans',
                        line=dict(color=colors[idx], width=2),
                        mode='lines+markers',
                        hovertemplate=(
                            "<b>Date:</b> %{x}<br>" +
                            "<b>Count:</b> %{y}<br>" +
                            "<extra></extra>"
                        )
                    ),
                    row=1, col=2
                )

            # Panel 3: Distribution Analysis
            scan_types = ['enter_scans', 'exit_scans', 'delivery_scans']
            for idx, scan_type in enumerate(scan_types):
                fig.add_trace(
                    go.Box(
                        y=df_work[scan_type].dropna(),
                        name=scan_type.replace('_', ' ').title(),
                        marker_color=colors[idx],
                        boxmean=True,
                        hovertemplate=(
                            "<b>%{text}</b><br>" +
                            "<b>Median:</b> %{median:.2f}<br>" +
                            "<b>Mean:</b> %{mean:.2f}<br>" +
                            "<b>Q1:</b> %{q1:.2f}<br>" +
                            "<b>Q3:</b> %{q3:.2f}<br>" +
                            "<extra></extra>"
                        ),
                        text=[scan_type.replace('_', ' ').title()] * len(df_work[scan_type].dropna())
                    ),
                    row=2, col=1
                )

            # Panel 4: Statistics Table
            stats_df = pd.DataFrame({
                'Metric': ['Total Count', 'Daily Average', 'Standard Dev', 'Maximum Daily'],
                'Enter Scans': [
                    len(df_work['enter_scans'].dropna()),
                    f"{df_grouped['enter_scans_count'].mean():.1f}",
                    f"{df_grouped['enter_scans_count'].std():.1f}",
                    df_grouped['enter_scans_count'].max()
                ],
                'Exit Scans': [
                    len(df_work['exit_scans'].dropna()),
                    f"{df_grouped['exit_scans_count'].mean():.1f}",
                    f"{df_grouped['exit_scans_count'].std():.1f}",
                    df_grouped['exit_scans_count'].max()
                ],
                'Delivery Scans': [
                    len(df_work['delivery_scans'].dropna()),
                    f"{df_grouped['delivery_scans_count'].mean():.1f}",
                    f"{df_grouped['delivery_scans_count'].std():.1f}",
                    df_grouped['delivery_scans_count'].max()
                ]
            })

            fig.add_trace(
                go.Table(
                    header=dict(
                        values=stats_df.columns.tolist(),
                        fill_color='rgba(70,130,180,0.8)',
                        align='left',
                        font=dict(color='white', size=12)
                    ),
                    cells=dict(
                        values=[stats_df[col] for col in stats_df.columns],
                        fill_color='rgba(240,248,255,0.6)',
                        align='left',
                        font=dict(size=11)
                    )
                ),
                row=2, col=2
            )

            # Create animation frames
            frames = [
                go.Frame(
                    data=[
                        # 3D Scatter
                        go.Scatter3d(
                            x=df_grouped['enter_scans_count'][:k+1],
                            y=df_grouped['exit_scans_count'][:k+1],
                            z=df_grouped['delivery_scans_count'][:k+1]
                        ),
                        # Trend Lines
                        *[go.Scatter(
                            x=df_grouped['date'][:k+1],
                            y=df_grouped[f'{scan_type}_scans_count'][:k+1]
                        ) for scan_type in ['enter', 'exit', 'delivery']]
                    ],
                    name=str(date)
                ) for k, date in enumerate(df_grouped['date'])
            ]
            fig.frames = frames

            # Update layout
            self._update_layout(fig)

            return fig

        except Exception as e:
            logger.error(f"Error creating animated time series: {str(e)}")
            logger.error("Full traceback:", exc_info=True)
            raise

    def _update_layout(self, fig: go.Figure) -> None:
        """Helper method to update figure layout"""
        fig.update_layout(
            title=dict(
                text='Scan Activity Analysis Dashboard',
                x=0.5,
                xanchor='center',
                font=dict(size=24, color='rgb(70,130,180)')
            ),
            scene=dict(
                xaxis_title="Enter Scans",
                yaxis_title="Exit Scans",
                zaxis_title="Delivery Scans"
            ),
            height=1000,
            width=1500,
            showlegend=True,
            legend=dict(
                yanchor="top",
                y=0.90,
                xanchor="right",
                x=0.0,
                bgcolor="rgba(255,255,255,0.9)",
                bordercolor="rgb(70,130,180)",
                borderwidth=1
            ),
            updatemenus=[self._create_animation_menu(), self._create_help_menu()]
        )

        self._add_help_annotation(fig)

    def _create_animation_menu(self) -> dict:
        """Create animation control menu"""
        return {
            "buttons": [
                {
                    "args": [None, {
                        "frame": {"duration": 500, "redraw": True},
                        "fromcurrent": True,
                        "transition": {"duration": 300, "easing": "quadratic-in-out"}
                    }],
                    "label": "▶ Play",
                    "method": "animate"
                },
                {
                    "args": [[None], {
                        "frame": {"duration": 0, "redraw": False},
                        "mode": "immediate",
                        "transition": {"duration": 0}
                    }],
                    "label": "⏸ Pause",
                    "method": "animate"
                }
            ],
            "direction": "left",
            "pad": {"r": 10, "t": 87},
            "showactive": True,
            "type": "buttons",
            "x": 0.1,
            "xanchor": "right",
            "y": 1.1,
            "yanchor": "top"
        }

    def _create_help_menu(self) -> dict:
        """Create help menu"""
        return {
            "buttons": [
                {
                    "args": [{"annotations[0].visible": True}],
                    "label": "Show Help",
                    "method": "relayout"
                },
                {
                    "args": [{"annotations[0].visible": False}],
                    "label": "Hide Help",
                    "method": "relayout"
                }
            ],
            "direction": "down",
            "showactive": True,
            "x": 0.9,
            "xanchor": "right",
            "y": 1.1,
            "yanchor": "top"
        }

    def _add_help_annotation(self, fig: go.Figure) -> None:
        """Add help text annotation to figure"""
        help_text = (
            "<b>Scan Activity Analysis Guide:</b><br><br>" +
            "• <b>3D View:</b> Shows relationships between different scan types<br>" +
            "• <b>Daily Trends:</b> Displays scan count patterns over time<br>" +
            "• <b>Distribution:</b> Shows statistical spread of scan activities<br>" +
            "• <b>Summary:</b> Provides key metrics for each scan type<br><br>" +
            "Interactive Features:<br>" +
            "• Use Play/Pause to control animation<br>" +
            "• Drag to rotate 3D view<br>" +
            "• Double-click to reset view<br>" +
            "• Click legend items to show/hide<br>" +
            "• Hover for detailed information"
        )

        fig.add_annotation(
            xref="paper", yref="paper",
            x=1, y=0,
            text=help_text,
            showarrow=False,
            align="center",
            bgcolor="rgba(255,255,255,0.95)",
            bordercolor="rgb(70,130,180)",
            borderwidth=2,
            font=dict(size=12),
            visible=True
        )

    def create_hierarchical_treemap(self, df: pd.DataFrame, search_results=None, selected_entity=None, global_filter=None) -> go.Figure:
        """
        Create a hierarchical treemap visualization from the input dataframe.

        This visualization shows the hierarchical relationship between complex_id,
        building_place_id, and unit_place_id with dynamic sizing based on scan counts.
        Different entity types are colored distinctly for easy identification, and
        detailed information is provided on hover.

        Args:
            df (pd.DataFrame): Input dataframe containing geographic data with hierarchical structure
            search_results (dict, optional): Dictionary containing search results to highlight
            selected_entity (dict, optional): Dictionary containing information about the selected entity
            global_filter (dict, optional): Current global filter state

        Returns:
            go.Figure: Plotly figure object containing the treemap visualization
        """
        logger.info("Creating hierarchical treemap visualization")

        # If global filter is active, highlight the selected entity
        if global_filter and not selected_entity:
            selected_entity = {
                'type': global_filter['type'],
                'id': global_filter['id'],
                'actual_id': global_filter.get('actual_id'),
                'highlight': global_filter.get('highlight', True),  # Use highlight flag from global_filter if available
                'auto_expand': True,
                'hierarchical_context': global_filter.get('hierarchical_context', {})
            }
            logger.info(f"Created selected entity from global filter: {selected_entity}")

        # Validate input parameters
        if not isinstance(df, pd.DataFrame):
            logger.error(f"Expected DataFrame for df, got {type(df)}")
            raise ValueError(f"Expected DataFrame for df, got {type(df)}")

        if search_results is not None and not isinstance(search_results, dict):
            logger.warning(f"Expected dict for search_results, got {type(search_results)}. Ignoring search results.")
            search_results = None

        if selected_entity is not None and not isinstance(selected_entity, dict):
            logger.warning(f"Expected dict for selected_entity, got {type(selected_entity)}. Ignoring selected entity.")
            selected_entity = None

        # Build hierarchical dataframe
        hierarchical_df = self._build_hierarchical_dataframe(df)

        # Define entity-specific colors
        entity_colors = {
            'root': '#6baed6',      # Blue
            'complex': '#fd8d3c',   # Orange
            'building': '#74c476',  # Green
            'unit': '#9e9ac8'       # Purple
        }

        # Define highlight colors (brighter versions of the entity colors)
        highlight_colors = {
            'root': '#a6d2f0',      # Lighter Blue
            'complex': '#ffb380',   # Lighter Orange
            'building': '#a8e6a8',  # Lighter Green
            'unit': '#c7c1e8'       # Lighter Purple
        }

        # Define selected entity color (bright orange-red)
        selected_color = '#FF5733'  # This is the color used for highlighting selected entities

        # Assign colors based on entity level
        hierarchical_df['color'] = hierarchical_df['level'].map(entity_colors)

        # Initialize highlighted_paths list outside the conditional block
        highlighted_paths = []

        # Apply highlighting for search results if provided
        if search_results and isinstance(search_results, dict):
            # Extract search matches
            complex_matches = search_results.get('complex_matches', [])
            building_matches = search_results.get('building_matches', [])
            unit_matches = search_results.get('unit_matches', [])

            # Highlight matching complexes
            if complex_matches:
                for complex_id in complex_matches:
                    # Convert complex_id to string for comparison
                    complex_id_str = str(complex_id)
                    # Find matching rows where complex_id is in the ID string
                    mask = (hierarchical_df['level'] == 'complex') & \
                           (hierarchical_df['id'].str.contains(f": {complex_id_str}$", regex=True))
                    hierarchical_df.loc[mask, 'color'] = highlight_colors['complex']

                    # Add to highlighted paths
                    if any(mask):
                        highlighted_paths.append(hierarchical_df.loc[mask, 'id'].iloc[0])

            # Highlight matching buildings
            if building_matches:
                for building_id in building_matches:
                    # Convert building_id to string for comparison
                    building_id_str = str(building_id)
                    # Find matching rows where building_id is in the ID string
                    mask = (hierarchical_df['level'] == 'building') & \
                           (hierarchical_df['id'].str.contains(f": {building_id_str}$", regex=True))
                    hierarchical_df.loc[mask, 'color'] = highlight_colors['building']

                    # Add to highlighted paths
                    if any(mask):
                        highlighted_paths.append(hierarchical_df.loc[mask, 'id'].iloc[0])

            # Highlight matching units
            if unit_matches:
                for unit_id in unit_matches:
                    # Convert unit_id to string for comparison
                    unit_id_str = str(unit_id)

                    # Try multiple matching strategies with detailed logging
                    match_found = False
                    logger.info(f"Searching for unit ID: {unit_id_str} in hierarchical dataframe")

                    # Log available unit IDs for debugging
                    unit_ids = hierarchical_df[hierarchical_df['level'] == 'unit']['id'].tolist()
                    logger.info(f"Available unit IDs in hierarchical dataframe: {unit_ids[:5]}... (total: {len(unit_ids)})")

                    # Strategy 1: Exact match with format "Unit: USC4826147912"
                    mask = (hierarchical_df['level'] == 'unit') & \
                           (hierarchical_df['id'] == f"Unit: {unit_id_str}")
                    logger.info(f"Strategy 1 (Exact match): {any(mask)}")

                    # Strategy 2: Match at the end of the string
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['id'].str.endswith(unit_id_str))
                        logger.info(f"Strategy 2 (Endswith): {any(mask)}")

                    # Strategy 3: Case-insensitive contains
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['id'].str.lower().str.contains(unit_id_str.lower(), regex=False))
                        logger.info(f"Strategy 3 (Contains): {any(mask)}")

                    # Strategy 4: Match unit_id column directly
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['unit_id'] == unit_id)
                        logger.info(f"Strategy 4 (Unit ID column): {any(mask)}")

                    # Strategy 5: Match on unit_id_clean field
                    if not any(mask):
                        # Clean the unit ID (remove special chars)
                        unit_id_clean = re.sub(r'[^a-zA-Z0-9]', '', unit_id_str)
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['unit_id_clean'] == unit_id_clean)
                        logger.info(f"Strategy 5 (Clean match): {any(mask)}")

                    # Strategy 6: Match on unit_id_numeric field
                    if not any(mask):
                        # Get just the numeric part
                        unit_id_numeric = re.sub(r'[^0-9]', '', unit_id_str)
                        if unit_id_numeric and len(unit_id_numeric) >= 4:  # Only use if we have at least 4 digits
                            mask = (hierarchical_df['level'] == 'unit') & \
                                   (hierarchical_df['unit_id_numeric'] == unit_id_numeric)
                            logger.info(f"Strategy 6 (Numeric match): {any(mask)}")

                    # Strategy 7: If it's a USC ID, try matching just the numeric part in the ID
                    if not any(mask) and unit_id_str.lower().startswith('usc'):
                        numeric_part = re.sub(r'[^0-9]', '', unit_id_str)
                        if numeric_part:
                            mask = (hierarchical_df['level'] == 'unit') & \
                                   (hierarchical_df['id'].str.contains(numeric_part, regex=False))
                            logger.info(f"Strategy 7 (Numeric part in ID): {any(mask)}")

                    # Strategy 8: Direct match on unit_id_lower field
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['unit_id_lower'] == unit_id_str.lower())
                        logger.info(f"Strategy 8 (Lowercase unit_id): {any(mask)}")

                    # Strategy 9: Match on search_text field
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['search_text'].str.contains(unit_id_str, regex=False))
                        logger.info(f"Strategy 9 (Search text): {any(mask)}")

                    # Strategy 10: Partial match on numeric part in search_text
                    if not any(mask):
                        numeric_part = re.sub(r'[^0-9]', '', unit_id_str)
                        if numeric_part and len(numeric_part) >= 4:  # Only use if we have at least 4 digits
                            mask = (hierarchical_df['level'] == 'unit') & \
                                   (hierarchical_df['search_text'].str.contains(numeric_part, regex=False))
                            logger.info(f"Strategy 10 (Numeric part in search_text): {any(mask)}")

                    # Apply highlighting if we found a match
                    if any(mask):
                        match_found = True
                        # Highlight the unit
                        hierarchical_df.loc[mask, 'color'] = highlight_colors['unit']

                        # Add to highlighted paths
                        unit_path = hierarchical_df.loc[mask, 'id'].iloc[0]
                        highlighted_paths.append(unit_path)

                        # Log the match for debugging
                        logger.info(f"Found match for unit {unit_id_str}: {unit_path}")

                        # Also highlight the path to this entity
                        unit_row = hierarchical_df[mask]
                        if not unit_row.empty:
                            # Highlight the parent building
                            parent_id = unit_row.iloc[0]['parent']
                            parent_mask = hierarchical_df['id'] == parent_id
                            hierarchical_df.loc[parent_mask, 'color'] = highlight_colors['building']

                            # Add parent to highlighted paths
                            if any(parent_mask):
                                highlighted_paths.append(parent_id)
                                logger.info(f"Added parent to highlighted paths: {parent_id}")

                            # Highlight the grandparent complex if it exists
                            if not hierarchical_df[parent_mask].empty:
                                grandparent_id = hierarchical_df[parent_mask].iloc[0]['parent']
                                grandparent_mask = hierarchical_df['id'] == grandparent_id
                                hierarchical_df.loc[grandparent_mask, 'color'] = highlight_colors['complex']

                                # Add grandparent to highlighted paths
                                if any(grandparent_mask):
                                    highlighted_paths.append(grandparent_id)
                                    logger.info(f"Added grandparent to highlighted paths: {grandparent_id}")

                    # If no match found with any strategy
                    if not match_found:
                        # Log that we couldn't find a match
                        logger.warning(f"Could not find a match for unit {unit_id_str} in the hierarchical dataframe")
                        # Print all unit IDs for debugging
                        unit_ids = hierarchical_df[hierarchical_df['level'] == 'unit']['id'].tolist()
                        logger.info(f"Available unit IDs: {unit_ids[:10]}... (total: {len(unit_ids)})")

                        # Fallback: If we have complex_id, try to highlight at least the building level
                        if 'complex_id' in df.columns and not df['complex_id'].isna().all():
                            logger.info("Attempting fallback highlighting at building level")

                            # Find the building that contains this unit
                            unit_row = df[df['unit_place_id'] == unit_id]
                            if not unit_row.empty:
                                building_id = unit_row.iloc[0]['building_place_id']
                                complex_id = unit_row.iloc[0]['complex_id'] if 'complex_id' in unit_row.iloc[0] else None

                                logger.info(f"Fallback: Found building {building_id} for unit {unit_id}")

                                # Highlight the building
                                building_mask = (hierarchical_df['level'] == 'building') & \
                                               (hierarchical_df['building_id'] == building_id)
                                if any(building_mask):
                                    hierarchical_df.loc[building_mask, 'color'] = highlight_colors['building']
                                    building_path = hierarchical_df.loc[building_mask, 'id'].iloc[0]
                                    highlighted_paths.append(building_path)
                                    logger.info(f"Fallback: Highlighted building {building_path}")

                                    # Also highlight the complex if available
                                    if complex_id is not None:
                                        complex_mask = (hierarchical_df['level'] == 'complex') & \
                                                      (hierarchical_df['complex_id'] == complex_id)
                                        if any(complex_mask):
                                            hierarchical_df.loc[complex_mask, 'color'] = highlight_colors['complex']
                                            complex_path = hierarchical_df.loc[complex_mask, 'id'].iloc[0]
                                            highlighted_paths.append(complex_path)
                                            logger.info(f"Fallback: Highlighted complex {complex_path}")

        # Apply special highlighting for selected entity if provided
        if selected_entity and isinstance(selected_entity, dict):
            entity_type = selected_entity.get('type')
            entity_id = selected_entity.get('id')
            auto_expand = selected_entity.get('auto_expand', False)
            highlight = selected_entity.get('highlight', True)  # Default to True if not specified

            # Log the selected entity for debugging
            logger.info(f"Applying highlighting for selected entity: {entity_type} {entity_id} (highlight={highlight}, auto_expand={auto_expand})")

            # Force highlight to True for direct treemap clicks
            if selected_entity.get('source') == 'treemap_click':
                highlight = True
                logger.info("Forcing highlight=True for treemap click")

            if entity_type and entity_id:
                # Convert entity_id to string for comparison
                entity_id_str = str(entity_id)

                # Track the path to the selected entity for expansion
                selected_path = None

                if entity_type == 'complex':
                    # For treemap clicks, we can directly match the ID
                    if selected_entity.get('source') == 'treemap_click':
                        mask = hierarchical_df['id'] == entity_id_str
                        logger.info(f"Direct ID match for treemap click: {entity_id_str}")
                    else:
                        # Find matching rows where complex_id is in the ID string
                        mask = (hierarchical_df['level'] == 'complex') & \
                               (hierarchical_df['id'].str.contains(f": {entity_id_str}$", regex=True))

                    # Apply highlighting if requested
                    if highlight:
                        hierarchical_df.loc[mask, 'color'] = selected_color
                        logger.info(f"Highlighted complex: {entity_id_str} with color {selected_color}")

                    # Get the selected path for expansion
                    if any(mask) and auto_expand:
                        selected_path = hierarchical_df.loc[mask, 'id'].iloc[0]
                        highlighted_paths.append(selected_path)
                        logger.info(f"Added complex path for expansion: {selected_path}")

                    # Also highlight the path to this entity
                    complex_row = hierarchical_df[mask]
                    if not complex_row.empty:
                        # Get the ID of the complex to find its children
                        complex_id_full = complex_row.iloc[0]['id']
                        # Highlight all buildings and units under this complex
                        building_mask = hierarchical_df['parent'] == complex_id_full
                        hierarchical_df.loc[building_mask, 'color'] = highlight_colors['building']
                        logger.info(f"Highlighted {sum(building_mask)} buildings under complex {entity_id_str}")

                elif entity_type == 'building':
                    # For treemap clicks, we can directly match the ID
                    if selected_entity.get('source') == 'treemap_click':
                        mask = hierarchical_df['id'] == entity_id_str
                        logger.info(f"Direct ID match for treemap click: {entity_id_str}")
                    else:
                        # Find matching rows where building_id is in the ID string
                        mask = (hierarchical_df['level'] == 'building') & \
                               (hierarchical_df['id'].str.contains(f": {entity_id_str}$", regex=True))

                    # Apply highlighting if requested
                    if highlight:
                        hierarchical_df.loc[mask, 'color'] = selected_color
                        logger.info(f"Highlighted building: {entity_id_str} with color {selected_color}")

                    # Get the selected path for expansion
                    if any(mask) and auto_expand:
                        selected_path = hierarchical_df.loc[mask, 'id'].iloc[0]
                        highlighted_paths.append(selected_path)
                        logger.info(f"Added building path for expansion: {selected_path}")

                    # Also highlight the path to this entity
                    building_row = hierarchical_df[mask]
                    if not building_row.empty:
                        # Get the ID of the building to find its children
                        building_id_full = building_row.iloc[0]['id']
                        # Highlight all units under this building
                        unit_mask = hierarchical_df['parent'] == building_id_full
                        hierarchical_df.loc[unit_mask, 'color'] = highlight_colors['unit']
                        logger.info(f"Highlighted {sum(unit_mask)} units under building {entity_id_str}")

                        # Highlight the parent complex if it exists
                        parent_id = building_row.iloc[0]['parent']
                        parent_mask = hierarchical_df['id'] == parent_id
                        hierarchical_df.loc[parent_mask, 'color'] = highlight_colors['complex']
                        logger.info(f"Highlighted parent complex for building {entity_id_str}")

                        # Add parent to highlighted paths for expansion
                        if auto_expand:
                            highlighted_paths.append(parent_id)
                            logger.info(f"Added parent complex path for expansion: {parent_id}")

                elif entity_type == 'unit':
                    # For treemap clicks, we can directly match the ID
                    if selected_entity.get('source') == 'treemap_click':
                        mask = hierarchical_df['id'] == entity_id_str
                        match_found = any(mask)
                        logger.info(f"Direct ID match for treemap click: {entity_id_str}, match found: {match_found}")
                    else:
                        # Try multiple matching strategies with detailed logging
                        match_found = False
                        logger.info(f"Searching for selected unit ID: {entity_id_str} in hierarchical dataframe")

                    # Log available unit IDs for debugging
                    unit_ids = hierarchical_df[hierarchical_df['level'] == 'unit']['id'].tolist()
                    logger.info(f"Available unit IDs in hierarchical dataframe: {unit_ids[:5]}... (total: {len(unit_ids)})")

                    # Strategy 1: Exact match with format "Unit: USC4826147912"
                    mask = (hierarchical_df['level'] == 'unit') & \
                           (hierarchical_df['id'] == f"Unit: {entity_id_str}")
                    logger.info(f"Strategy 1 (Exact match): {any(mask)}")

                    # Strategy 2: Match at the end of the string
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['id'].str.endswith(entity_id_str))
                        logger.info(f"Strategy 2 (Endswith): {any(mask)}")

                    # Strategy 3: Case-insensitive contains
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['id'].str.lower().str.contains(entity_id_str.lower(), regex=False))
                        logger.info(f"Strategy 3 (Contains): {any(mask)}")

                    # Strategy 4: Match unit_id column directly
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['unit_id'] == entity_id)
                        logger.info(f"Strategy 4 (Unit ID column): {any(mask)}")

                    # Strategy 5: Match on unit_id_clean field
                    if not any(mask):
                        # Clean the entity ID (remove special chars)
                        entity_id_clean = re.sub(r'[^a-zA-Z0-9]', '', entity_id_str)
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['unit_id_clean'] == entity_id_clean)
                        logger.info(f"Strategy 5 (Clean match): {any(mask)}")

                    # Strategy 6: Match on unit_id_numeric field
                    if not any(mask):
                        # Get just the numeric part
                        entity_id_numeric = re.sub(r'[^0-9]', '', entity_id_str)
                        if entity_id_numeric and len(entity_id_numeric) >= 4:  # Only use if we have at least 4 digits
                            mask = (hierarchical_df['level'] == 'unit') & \
                                   (hierarchical_df['unit_id_numeric'] == entity_id_numeric)
                            logger.info(f"Strategy 6 (Numeric match): {any(mask)}")

                    # Strategy 7: If it's a USC ID, try matching just the numeric part in the ID
                    if not any(mask) and entity_id_str.lower().startswith('usc'):
                        numeric_part = re.sub(r'[^0-9]', '', entity_id_str)
                        if numeric_part:
                            mask = (hierarchical_df['level'] == 'unit') & \
                                   (hierarchical_df['id'].str.contains(numeric_part, regex=False))
                            logger.info(f"Strategy 7 (Numeric part in ID): {any(mask)}")

                    # Strategy 8: Direct match on unit_id_lower field
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['unit_id_lower'] == entity_id_str.lower())
                        logger.info(f"Strategy 8 (Lowercase unit_id): {any(mask)}")

                    # Strategy 9: Match on search_text field
                    if not any(mask):
                        mask = (hierarchical_df['level'] == 'unit') & \
                               (hierarchical_df['search_text'].str.contains(entity_id_str, regex=False))
                        logger.info(f"Strategy 9 (Search text): {any(mask)}")

                    # Strategy 10: Partial match on numeric part in search_text
                    if not any(mask):
                        numeric_part = re.sub(r'[^0-9]', '', entity_id_str)
                        if numeric_part and len(numeric_part) >= 4:  # Only use if we have at least 4 digits
                            mask = (hierarchical_df['level'] == 'unit') & \
                                   (hierarchical_df['search_text'].str.contains(numeric_part, regex=False))
                            logger.info(f"Strategy 10 (Numeric part in search_text): {any(mask)}")

                    # Apply highlighting if we found a match
                    if any(mask):
                        match_found = True

                        # Highlight the unit if requested
                        if highlight:
                            hierarchical_df.loc[mask, 'color'] = selected_color
                            logger.info(f"Highlighted unit: {entity_id_str} with color {selected_color}")

                        # Get the selected path for expansion
                        selected_path = hierarchical_df.loc[mask, 'id'].iloc[0]
                        highlighted_paths.append(selected_path)
                        logger.info(f"Selected unit path: {selected_path}")

                        # Also highlight the path to this entity
                        unit_row = hierarchical_df[mask]
                        if not unit_row.empty:
                            # Highlight the parent building
                            parent_id = unit_row.iloc[0]['parent']
                            parent_mask = hierarchical_df['id'] == parent_id
                            hierarchical_df.loc[parent_mask, 'color'] = highlight_colors['building']
                            logger.info(f"Highlighted parent building for unit {entity_id_str}")

                            # Add parent to highlighted paths for expansion
                            highlighted_paths.append(parent_id)
                            logger.info(f"Added parent building to highlighted paths: {parent_id}")

                            # Highlight the grandparent complex if it exists
                            if not hierarchical_df[parent_mask].empty:
                                grandparent_id = hierarchical_df[parent_mask].iloc[0]['parent']
                                grandparent_mask = hierarchical_df['id'] == grandparent_id
                                hierarchical_df.loc[grandparent_mask, 'color'] = highlight_colors['complex']
                                logger.info(f"Highlighted grandparent complex for unit {entity_id_str}")

                                # Add grandparent to highlighted paths for expansion
                                highlighted_paths.append(grandparent_id)
                                logger.info(f"Added grandparent complex to highlighted paths: {grandparent_id}")

                    # If no match found with any strategy
                    if not match_found:
                        # Log that we couldn't find a match
                        logger.warning(f"Could not find a match for selected unit {entity_id_str} in the hierarchical dataframe")
                        # Print all unit IDs for debugging
                        unit_ids = hierarchical_df[hierarchical_df['level'] == 'unit']['id'].tolist()
                        logger.info(f"Available unit IDs: {unit_ids[:10]}... (total: {len(unit_ids)})")

                        # Fallback: If we have complex_id, try to highlight at least the building level
                        if 'complex_id' in df.columns and not df['complex_id'].isna().all():
                            logger.info("Attempting fallback highlighting at building level for selected entity")

                            # Find the building that contains this unit
                            unit_row = df[df['unit_place_id'] == entity_id]
                            if not unit_row.empty:
                                building_id = unit_row.iloc[0]['building_place_id']
                                complex_id = unit_row.iloc[0]['complex_id'] if 'complex_id' in unit_row.iloc[0] else None

                                logger.info(f"Fallback: Found building {building_id} for selected unit {entity_id}")

                                # Highlight the building if highlighting is requested
                                building_mask = (hierarchical_df['level'] == 'building') & \
                                               (hierarchical_df['building_id'] == building_id)
                                if any(building_mask):
                                    if highlight:
                                        hierarchical_df.loc[building_mask, 'color'] = selected_color
                                        logger.info(f"Fallback: Highlighted building for unit {entity_id_str} with color {selected_color}")

                                    building_path = hierarchical_df.loc[building_mask, 'id'].iloc[0]
                                    highlighted_paths.append(building_path)
                                    logger.info(f"Fallback: Added building path for expansion: {building_path}")

                                    # Also highlight the complex if available
                                    if complex_id is not None:
                                        complex_mask = (hierarchical_df['level'] == 'complex') & \
                                                      (hierarchical_df['complex_id'] == complex_id)
                                        if any(complex_mask):
                                            hierarchical_df.loc[complex_mask, 'color'] = highlight_colors['complex']
                                            complex_path = hierarchical_df.loc[complex_mask, 'id'].iloc[0]
                                            highlighted_paths.append(complex_path)
                                            logger.info(f"Fallback: Highlighted complex for unit {entity_id_str} and added to paths: {complex_path}")

                # Log the selected path for debugging
                if selected_path:
                    logger.info(f"Selected path for expansion: {selected_path}")

        # Create hover text with detailed information
        hierarchical_df['hover_text'] = hierarchical_df.apply(
            lambda row: self._create_treemap_hover_text(row),
            axis=1
        )

        # Log the color assignments for debugging
        if selected_entity and isinstance(selected_entity, dict):
            entity_id = selected_entity.get('id')
            if entity_id in hierarchical_df['id'].values:
                entity_color = hierarchical_df.loc[hierarchical_df['id'] == entity_id, 'color'].iloc[0]
                logger.info(f"Color assigned to selected entity {entity_id}: {entity_color}")
                # Force the selected color for the entity
                if selected_entity.get('highlight', True):
                    hierarchical_df.loc[hierarchical_df['id'] == entity_id, 'color'] = selected_color
                    logger.info(f"Forced selected color {selected_color} for entity {entity_id}")

        # Create the treemap figure
        fig = go.Figure(go.Treemap(
            ids=hierarchical_df['id'],
            labels=hierarchical_df['id'],
            parents=hierarchical_df['parent'],
            values=hierarchical_df['value'],
            branchvalues='total',
            marker=dict(
                colors=hierarchical_df['color'],
                line=dict(width=1, color='#FFFFFF')
            ),
            hovertext=hierarchical_df['hover_text'],
            hoverinfo='text',
            texttemplate='%{label}<br>%{value} scans',
            textposition='middle center',
            pathbar=dict(
                visible=True,
                thickness=20,
                side='top'
            ),
            # Auto-expand to show highlighted entities
            root=dict(
                color="aliceblue"
            ),
            # Set maximum depth to show all levels, including units in complex hierarchy
            maxdepth=4
        ))

        # Check if we have a complex hierarchy (complex -> building -> unit)
        has_complex = any(hierarchical_df['level'] == 'complex')
        logger.info(f"Dataset has complex hierarchy: {has_complex}")

        # Update layout for better appearance
        fig.update_layout(
            margin=dict(t=50, l=25, r=25, b=25),
            title="Hierarchical Scan Distribution",
            uniformtext=dict(minsize=12, mode='hide'),
            font=dict(family="Arial, sans-serif", size=14),
            autosize=True,
        )

        # Apply special configuration for complex hierarchies
        if has_complex:
            logger.info("Applying special configuration for complex hierarchy")
            # Ensure the treemap is configured to show all levels
            fig.update_traces(
                maxdepth=4,  # Ensure we can see all levels: root -> complex -> building -> unit
                root_color="aliceblue",
                tiling=dict(
                    packing="squarify",
                    pad=0
                )
            )

            # Add explicit configuration for complex hierarchies
            fig.update_layout(
                margin=dict(t=50, l=25, r=25, b=25, pad=4),
                autosize=True,
                # Use a custom colorway to make entities more visible
                colorway=["#f7fbff", "#deebf7", "#c6dbef", "#9ecae1", "#6baed6", "#4292c6", "#2171b5", "#08519c", "#08306b"]
            )

        # If we have search results, set up the treemap to expand to show them
        if search_results and isinstance(search_results, dict):
            # Log the highlighted paths
            logger.info(f"Highlighted paths: {highlighted_paths}")

            # Get the search term for reference
            search_term = search_results.get('search_term', '')
            logger.info(f"Search term: {search_term}")

            # Update the treemap colorway
            fig.update_layout(
                colorway=["#f7fbff", "#deebf7", "#c6dbef", "#9ecae1", "#6baed6", "#4292c6", "#2171b5", "#08519c", "#08306b"],
                margin=dict(t=50, l=25, r=25, b=25, pad=4),
                autosize=True
            )

            # Force the treemap to expand to show the highlighted paths
            if highlighted_paths:
                # Create a list of paths to expand
                expanded_paths = []
                for path in highlighted_paths:
                    # Add the path and all its parents to the expanded paths
                    current_path = path
                    while current_path != '':
                        expanded_paths.append(current_path)
                        # Find the parent of the current path
                        parent_row = hierarchical_df[hierarchical_df['id'] == current_path]
                        if not parent_row.empty:
                            current_path = parent_row.iloc[0]['parent']
                        else:
                            current_path = ''

                # Log the expanded paths
                logger.info(f"Expanded paths: {expanded_paths}")

                # Create a list of visible paths for the treemap
                visible_paths = []
                for path in expanded_paths:
                    # Extract the entity ID from the path
                    if ': ' in path:
                        entity_id = path.split(': ')[1]
                        visible_paths.append(entity_id)

                # Log the visible paths
                logger.info(f"Visible paths: {visible_paths}")

                # Update the treemap to expand these paths
                fig.update_traces(
                    root_color="aliceblue",
                    # Set the expanded paths
                    tiling=dict(
                        packing="squarify",
                        pad=0
                    ),
                    # Force visibility of the highlighted paths
                    visible=True,
                    # Set the maximum depth to show all levels, including units in complex hierarchy
                    maxdepth=4
                )

                # Add explicit path expansion for complex hierarchies
                if any(path.startswith('Unit:') for path in highlighted_paths):
                    # If we're highlighting a unit, make sure we expand to show it
                    logger.info("Applying explicit path expansion for unit in complex hierarchy")

                    # Find the deepest highlighted path (should be the unit)
                    unit_paths = [p for p in highlighted_paths if p.startswith('Unit:')]
                    if unit_paths:
                        unit_path = unit_paths[0]  # Take the first unit path
                        logger.info(f"Expanding to show unit path: {unit_path}")

                        # Get the full path from root to unit
                        path_to_unit = []
                        current = unit_path
                        while current != '':
                            path_to_unit.append(current)
                            parent_row = hierarchical_df[hierarchical_df['id'] == current]
                            if not parent_row.empty:
                                current = parent_row.iloc[0]['parent']
                            else:
                                current = ''

                        # Reverse to get root -> complex -> building -> unit order
                        path_to_unit.reverse()
                        logger.info(f"Full path to unit: {path_to_unit}")

                        # Update layout to ensure the path is expanded
                        fig.update_layout(
                            margin=dict(t=50, l=25, r=25, b=25, pad=4),
                            autosize=True,
                            # Use a custom colorway to make highlighted entities more visible
                            colorway=["#f7fbff", "#deebf7", "#c6dbef", "#9ecae1", "#6baed6", "#4292c6", "#2171b5", "#08519c", "#08306b"]
                        )

        # Add explanation
        explanation = """
        <b>Hierarchical Treemap Visualization:</b><br>
        This treemap shows the hierarchical relationship between complexes, buildings, and units.<br><br>
        <b>Key Features:</b><br>
        • Rectangle size represents the number of scans<br>
        • Colors indicate different entity types (complex, building, unit)<br>
        • Click on rectangles to drill down into the hierarchy<br>
        • Use the path bar at the top to navigate back up<br>
        • Hover for detailed scan information<br><br>
        <b>Usage:</b><br>
        • Analyze distribution of scans across the hierarchy<br>
        • Identify high-activity areas<br>
        • Compare scan types across different entities
        """
        self.add_explanation(fig, explanation, title="Hierarchical Treemap")
        self.add_timestamp(fig)

        return fig

    def _build_hierarchical_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Build a hierarchical dataframe for treemap visualization.

        This function transforms the input dataframe into a format suitable for
        treemap visualization, calculating counts of different scan types for each
        entity in the hierarchy.

        Args:
            df (pd.DataFrame): Input dataframe containing geographic data with hierarchical structure

        Returns:
            pd.DataFrame: Transformed dataframe with hierarchical structure for treemap visualization
        """
        logger.info("Building hierarchical dataframe for treemap visualization")

        # Check if complex_id is present in the dataframe
        has_complex_id = 'complex_id' in df.columns and not df['complex_id'].isna().all()

        # Create a list to store hierarchical data
        hierarchical_data = []

        # Add the root node
        total_entry = df['enter_latitude'].notna().sum()
        total_exit = df['exit_latitude'].notna().sum()
        total_delivery = df['scan_latitude'].notna().sum()
        total_scans = total_entry + total_exit + total_delivery

        hierarchical_data.append({
            'id': 'All Data',
            'parent': '',
            'value': total_scans,
            'entry_scans': total_entry,
            'exit_scans': total_exit,
            'delivery_scans': total_delivery,
            'level': 'root',
            'search_text': 'all data root'
        })

        # Process complex level if available
        if has_complex_id:
            for complex_id in df['complex_id'].dropna().unique():
                complex_df = df[df['complex_id'] == complex_id]

                # Count scans for this complex
                complex_entry = complex_df['enter_latitude'].notna().sum()
                complex_exit = complex_df['exit_latitude'].notna().sum()
                complex_delivery = complex_df['scan_latitude'].notna().sum()
                complex_total = complex_entry + complex_exit + complex_delivery

                hierarchical_data.append({
                    'id': f"Complex: {complex_id}",
                    'parent': 'All Data',
                    'value': complex_total,
                    'entry_scans': complex_entry,
                    'exit_scans': complex_exit,
                    'delivery_scans': complex_delivery,
                    'level': 'complex',
                    'complex_id': complex_id,
                    'search_text': f"complex {complex_id}"
                })

                # Process buildings within this complex
                for building_id in complex_df['building_place_id'].unique():
                    building_df = complex_df[complex_df['building_place_id'] == building_id]

                    # Count scans for this building
                    building_entry = building_df['enter_latitude'].notna().sum()
                    building_exit = building_df['exit_latitude'].notna().sum()
                    building_delivery = building_df['scan_latitude'].notna().sum()
                    building_total = building_entry + building_exit + building_delivery

                    hierarchical_data.append({
                        'id': f"Building: {building_id}",
                        'parent': f"Complex: {complex_id}",
                        'value': building_total,
                        'entry_scans': building_entry,
                        'exit_scans': building_exit,
                        'delivery_scans': building_delivery,
                        'level': 'building',
                        'complex_id': complex_id,
                        'building_id': building_id,
                        'search_text': f"building {building_id} complex {complex_id}"
                    })

                    # Process units within this building
                    for unit_id in building_df['unit_place_id'].unique():
                        unit_df = building_df[building_df['unit_place_id'] == unit_id]

                        # Count scans for this unit
                        unit_entry = unit_df['enter_latitude'].notna().sum()
                        unit_exit = unit_df['exit_latitude'].notna().sum()
                        unit_delivery = unit_df['scan_latitude'].notna().sum()
                        unit_total = unit_entry + unit_exit + unit_delivery

                        # Create a searchable ID string
                        unit_id_str = str(unit_id)

                        # Create additional searchable fields for better matching
                        unit_id_clean = re.sub(r'[^a-zA-Z0-9]', '', unit_id_str)
                        unit_id_numeric = re.sub(r'[^0-9]', '', unit_id_str)

                        # Create a comprehensive search text that includes all variations
                        search_text = f"unit {unit_id} {unit_id_clean} {unit_id_numeric} building {building_id} complex {complex_id}"

                        hierarchical_data.append({
                            'id': f"Unit: {unit_id}",
                            'parent': f"Building: {building_id}",
                            'value': unit_total,
                            'entry_scans': unit_entry,
                            'exit_scans': unit_exit,
                            'delivery_scans': unit_delivery,
                            'level': 'unit',
                            'complex_id': complex_id,
                            'building_id': building_id,
                            'unit_id': unit_id,
                            'unit_id_lower': unit_id_str.lower(),  # Add lowercase version for case-insensitive search
                            'unit_id_clean': unit_id_clean,  # Add cleaned version (no special chars)
                            'unit_id_numeric': unit_id_numeric,  # Add numeric-only version
                            'search_text': search_text
                        })
        else:
            # If no complex_id, start with buildings
            for building_id in df['building_place_id'].unique():
                building_df = df[df['building_place_id'] == building_id]

                # Count scans for this building
                building_entry = building_df['enter_latitude'].notna().sum()
                building_exit = building_df['exit_latitude'].notna().sum()
                building_delivery = building_df['scan_latitude'].notna().sum()
                building_total = building_entry + building_exit + building_delivery

                hierarchical_data.append({
                    'id': f"Building: {building_id}",
                    'parent': 'All Data',
                    'value': building_total,
                    'entry_scans': building_entry,
                    'exit_scans': building_exit,
                    'delivery_scans': building_delivery,
                    'level': 'building',
                    'building_id': building_id,
                    'search_text': f"building {building_id}"
                })

                # Process units within this building
                for unit_id in building_df['unit_place_id'].unique():
                    unit_df = building_df[building_df['unit_place_id'] == unit_id]

                    # Count scans for this unit
                    unit_entry = unit_df['enter_latitude'].notna().sum()
                    unit_exit = unit_df['exit_latitude'].notna().sum()
                    unit_delivery = unit_df['scan_latitude'].notna().sum()
                    unit_total = unit_entry + unit_exit + unit_delivery

                    # Create a searchable ID string
                    unit_id_str = str(unit_id)

                    # Create additional searchable fields for better matching
                    unit_id_clean = re.sub(r'[^a-zA-Z0-9]', '', unit_id_str)
                    unit_id_numeric = re.sub(r'[^0-9]', '', unit_id_str)

                    # Create a comprehensive search text that includes all variations
                    search_text = f"unit {unit_id} {unit_id_clean} {unit_id_numeric} building {building_id}"

                    hierarchical_data.append({
                        'id': f"Unit: {unit_id}",
                        'parent': f"Building: {building_id}",
                        'value': unit_total,
                        'entry_scans': unit_entry,
                        'exit_scans': unit_exit,
                        'delivery_scans': unit_delivery,
                        'level': 'unit',
                        'building_id': building_id,
                        'unit_id': unit_id,
                        'unit_id_lower': unit_id_str.lower(),  # Add lowercase version for case-insensitive search
                        'unit_id_clean': unit_id_clean,  # Add cleaned version (no special chars)
                        'unit_id_numeric': unit_id_numeric,  # Add numeric-only version
                        'search_text': search_text
                    })

        # Convert to dataframe
        hierarchical_df = pd.DataFrame(hierarchical_data)
        logger.info(f"Created hierarchical dataframe with {len(hierarchical_df)} rows")

        # Log detailed information about the hierarchical structure
        level_counts = hierarchical_df['level'].value_counts().to_dict()
        logger.info(f"Hierarchical structure: {level_counts}")

        # Log sample of each level for debugging
        for level in ['root', 'complex', 'building', 'unit']:
            if level in level_counts and level_counts[level] > 0:
                sample = hierarchical_df[hierarchical_df['level'] == level].head(3)
                logger.info(f"Sample {level} rows: {sample[['id', 'parent', 'level']].to_dict('records')}")

        return hierarchical_df

    def _create_treemap_hover_text(self, row: pd.Series) -> str:
        """
        Create detailed hover text for a treemap node.

        Args:
            row (pd.Series): DataFrame row containing node information

        Returns:
            str: Formatted hover text string
        """
        total_scans = row['value']
        if total_scans == 0:
            return f"<b>{row['id']}</b><br>No scans available"

        # Basic information
        text = f"<b>{row['id']}</b><br>"
        text += f"Total Scans: {total_scans}<br>"

        # Add scan type breakdown
        text += f"Entry Scans: {row['entry_scans']} ({row['entry_scans']/total_scans*100:.1f}%)<br>"
        text += f"Exit Scans: {row['exit_scans']} ({row['exit_scans']/total_scans*100:.1f}%)<br>"
        text += f"Delivery Scans: {row['delivery_scans']} ({row['delivery_scans']/total_scans*100:.1f}%)<br>"

        # Add IDs for reference
        if row['level'] == 'complex':
            text += f"<br>Complex ID: {row['complex_id']}"
        elif row['level'] == 'building':
            text += f"<br>Building ID: {row['building_id']}"
            if 'complex_id' in row:
                text += f"<br>Complex ID: {row['complex_id']}"
        elif row['level'] == 'unit':
            text += f"<br>Unit ID: {row['unit_id']}"
            text += f"<br>Building ID: {row['building_id']}"
            if 'complex_id' in row:
                text += f"<br>Complex ID: {row['complex_id']}"

        return text

    # The add_explanation method has been consolidated with the main method above

    def add_timestamp(self, fig: go.Figure) -> None:
        """
        Add a timestamp to the figure.

        Args:
            fig (go.Figure): The figure to add the timestamp to
        """
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        fig.add_annotation(
            xref="paper", yref="paper",
            x=0.99, y=0.01,
            text=f"Generated: {timestamp}",
            showarrow=False,
            align="right",
            bgcolor="rgba(255,255,255,0.8)",
            font=dict(size=10, color="gray"),
            visible=True
        )

    def classify_scan_consistency(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Classify each record based on the consistency of its scan types.

        For each record, all three scan types (entry, exit, delivery) should be present or all missing.
        Any other pattern indicates a data capture issue or discrepancy.

        Args:
            df (pd.DataFrame): DataFrame with scan data

        Returns:
            pd.DataFrame: DataFrame with added scan consistency classification
        """
        # Create a copy to avoid modifying the original
        df_copy = df.copy()

        # Check if required columns exist
        has_entry_cols = 'enter_latitude' in df_copy.columns and 'enter_longitude' in df_copy.columns
        has_exit_cols = 'exit_latitude' in df_copy.columns and 'exit_longitude' in df_copy.columns
        has_delivery_cols = False

        # Check for delivery scan coordinates (scan_latitude and scan_longitude)
        if 'scan_latitude' in df_copy.columns and 'scan_longitude' in df_copy.columns:
            has_delivery_cols = True
            delivery_lat_col = 'scan_latitude'
            delivery_lng_col = 'scan_longitude'
        # Fallback to alternative column names if the primary ones aren't found
        elif 'delivery_latitude' in df_copy.columns and 'delivery_longitude' in df_copy.columns:
            has_delivery_cols = True
            delivery_lat_col = 'delivery_latitude'
            delivery_lng_col = 'delivery_longitude'
        elif 'deliver_latitude' in df_copy.columns and 'deliver_longitude' in df_copy.columns:
            has_delivery_cols = True
            delivery_lat_col = 'deliver_latitude'
            delivery_lng_col = 'deliver_longitude'

        # Create flags for each scan type
        if has_entry_cols:
            df_copy['has_entry'] = ~(df_copy['enter_latitude'].isna() | df_copy['enter_longitude'].isna())
        else:
            df_copy['has_entry'] = False

        if has_exit_cols:
            df_copy['has_exit'] = ~(df_copy['exit_latitude'].isna() | df_copy['exit_longitude'].isna())
        else:
            df_copy['has_exit'] = False

        if has_delivery_cols:
            df_copy['has_delivery'] = ~(df_copy[delivery_lat_col].isna() | df_copy[delivery_lng_col].isna())
        else:
            df_copy['has_delivery'] = False

        # Create consistency classification
        conditions = [
            (df_copy['has_entry'] & df_copy['has_exit'] & df_copy['has_delivery']),  # Complete
            (~df_copy['has_entry'] & ~df_copy['has_exit'] & ~df_copy['has_delivery']),  # All Missing
            (df_copy['has_entry'] & ~df_copy['has_exit'] & ~df_copy['has_delivery']),  # Entry Only
            (~df_copy['has_entry'] & df_copy['has_exit'] & ~df_copy['has_delivery']),  # Exit Only
            (~df_copy['has_entry'] & ~df_copy['has_exit'] & df_copy['has_delivery']),  # Delivery Only
            (~df_copy['has_entry'] & df_copy['has_exit'] & df_copy['has_delivery']),  # Missing Entry
            (df_copy['has_entry'] & ~df_copy['has_exit'] & df_copy['has_delivery']),  # Missing Exit
            (df_copy['has_entry'] & df_copy['has_exit'] & ~df_copy['has_delivery']),  # Missing Delivery
        ]

        categories = [
            'Complete',
            'All Missing',
            'Entry Only',
            'Exit Only',
            'Delivery Only',
            'Missing Entry',
            'Missing Exit',
            'Missing Delivery'
        ]

        import numpy as np
        df_copy['scan_consistency'] = np.select(conditions, categories, default='Unknown')

        # Flag records with discrepancies (not Complete or All Missing)
        df_copy['has_discrepancy'] = ~df_copy['scan_consistency'].isin(['Complete', 'All Missing'])

        return df_copy

    def calculate_consistency_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate metrics based on scan consistency classification, respecting the hierarchical
        structure of the data (complex_id > building_place_id > unit_place_id > address_id).

        Args:
            df (pd.DataFrame): DataFrame with scan consistency classification

        Returns:
            Dict[str, Any]: Dictionary containing calculated metrics at various hierarchical levels
        """
        # Calculate counts and percentages for each category
        consistency_counts = df['scan_consistency'].value_counts().to_dict()
        consistency_percent = (df['scan_consistency'].value_counts(normalize=True) * 100).to_dict()

        # Calculate overall discrepancy rate
        discrepancy_rate = df['has_discrepancy'].mean() * 100

        # Calculate missing scan counts for each type
        total_records = len(df)
        missing_entry_count = df[~df['has_entry']].shape[0]
        missing_exit_count = df[~df['has_exit']].shape[0]
        missing_delivery_count = df[~df['has_delivery']].shape[0]

        # Calculate missing scan percentages
        missing_entry_percent = (missing_entry_count / total_records) * 100 if total_records > 0 else 0
        missing_exit_percent = (missing_exit_count / total_records) * 100 if total_records > 0 else 0
        missing_delivery_percent = (missing_delivery_count / total_records) * 100 if total_records > 0 else 0

        # Calculate metrics by entity (complex, building, unit, address)
        entity_metrics = {}

        # Complex level metrics (if complex_id is available)
        if 'complex_id' in df.columns:
            complex_discrepancy = df.groupby('complex_id')['has_discrepancy'].agg(['mean', 'count'])
            complex_discrepancy['percent'] = complex_discrepancy['mean'] * 100

            # Calculate scan type distribution for each complex
            complex_scan_types = df.groupby('complex_id')['scan_consistency'].value_counts().unstack(fill_value=0)
            # Convert to percentages
            complex_total = complex_scan_types.sum(axis=1)
            complex_scan_pct = complex_scan_types.div(complex_total, axis=0) * 100

            # Sort by discrepancy percentage (highest first) and get top 10
            top_problematic_complexes = complex_discrepancy.sort_values('percent', ascending=False).head(10)
            entity_metrics['top_problematic_complexes'] = top_problematic_complexes.to_dict('index')

            # Add scan type distribution for top complexes
            for complex_id in top_problematic_complexes.index:
                if complex_id in complex_scan_pct.index:
                    entity_metrics[f'complex_{complex_id}_scan_types'] = complex_scan_pct.loc[complex_id].to_dict()

        # Building level metrics
        if 'building_place_id' in df.columns:
            building_discrepancy = df.groupby('building_place_id')['has_discrepancy'].agg(['mean', 'count'])
            building_discrepancy['percent'] = building_discrepancy['mean'] * 100

            # Calculate scan type distribution for each building
            building_scan_types = df.groupby('building_place_id')['scan_consistency'].value_counts().unstack(fill_value=0)
            # Convert to percentages
            building_total = building_scan_types.sum(axis=1)
            building_scan_pct = building_scan_types.div(building_total, axis=0) * 100

            # Sort by discrepancy percentage (highest first) and get top 10
            top_problematic_buildings = building_discrepancy.sort_values('percent', ascending=False).head(10)
            entity_metrics['top_problematic_buildings'] = top_problematic_buildings.to_dict('index')

            # Add scan type distribution for top buildings
            for building_id in top_problematic_buildings.index:
                if building_id in building_scan_pct.index:
                    entity_metrics[f'building_{building_id}_scan_types'] = building_scan_pct.loc[building_id].to_dict()

        # Unit level metrics
        if 'unit_place_id' in df.columns:
            unit_discrepancy = df.groupby('unit_place_id')['has_discrepancy'].agg(['mean', 'count'])
            unit_discrepancy['percent'] = unit_discrepancy['mean'] * 100

            # Calculate scan type distribution for each unit
            unit_scan_types = df.groupby('unit_place_id')['scan_consistency'].value_counts().unstack(fill_value=0)
            # Convert to percentages
            unit_total = unit_scan_types.sum(axis=1)
            unit_scan_pct = unit_scan_types.div(unit_total, axis=0) * 100

            # Sort by discrepancy percentage (highest first) and get top 10
            top_problematic_units = unit_discrepancy.sort_values('percent', ascending=False).head(10)
            entity_metrics['top_problematic_units'] = top_problematic_units.to_dict('index')

            # Add scan type distribution for top units
            for unit_id in top_problematic_units.index:
                if unit_id in unit_scan_pct.index:
                    entity_metrics[f'unit_{unit_id}_scan_types'] = unit_scan_pct.loc[unit_id].to_dict()

        # Address level metrics (if address_id is available)
        if 'address_id' in df.columns:
            address_discrepancy = df.groupby('address_id')['has_discrepancy'].agg(['mean', 'count'])
            address_discrepancy['percent'] = address_discrepancy['mean'] * 100

            # Sort by discrepancy percentage (highest first) and get top 10
            top_problematic_addresses = address_discrepancy.sort_values('percent', ascending=False).head(10)
            entity_metrics['top_problematic_addresses'] = top_problematic_addresses.to_dict('index')

        return {
            'consistency_counts': consistency_counts,
            'consistency_percent': consistency_percent,
            'discrepancy_rate': discrepancy_rate,
            'total_records': total_records,
            'missing_entry_count': missing_entry_count,
            'missing_exit_count': missing_exit_count,
            'missing_delivery_count': missing_delivery_count,
            'missing_entry_percent': missing_entry_percent,
            'missing_exit_percent': missing_exit_percent,
            'missing_delivery_percent': missing_delivery_percent,
            'entity_metrics': entity_metrics
        }

    def analyze_missing_scans(self, df: pd.DataFrame, df_raw: pd.DataFrame = None, global_filter: dict = None) -> Dict[str, Any]:
        """
        Analyze scan consistency and discrepancies to identify data capture issues.

        This method analyzes the dataset to identify inconsistencies between entry, exit, and delivery scans.
        For each record, all three scan types should be present or all missing. Any other pattern indicates
        a data capture issue or discrepancy. The method creates visualizations and tabular data to help
        operations teams identify and address these issues.

        The visualizations answer the following questions:
        1. What is missing? (Entry scan, Exit scan, Delivery scan, or Coordinate)
        2. Where is it missing? (Complex ID, Building ID, Unit ID, Address ID)
        3. When is it missing? (Timestamp/date, event window)
        4. Why is it missing? (Null entry, out-of-bounds coordinates, invalid type, etc.)
        5. How many records are affected?

        When both cleaned and raw data are provided, it also compares the changes in scan consistency
        after cleaning, showing:
        - The exact differences between raw input data and the cleaned output
        - Where missing values were filled and with what values
        - How the cleaning process improved scan consistency

        The visualizations include:
        1. Comprehensive Scan Consistency Summary: A detailed table showing scan presence and consistency
        2. Scan Consistency Distribution: A horizontal bar chart showing the distribution of consistency categories
        3. Unified Hierarchical Analysis: A multi-panel visualization showing metrics across hierarchical levels
        4. Unified Temporal and Spatial Analysis: A visualization showing patterns by time and location
        5. Root Causes of Missing Scans: A visualization analyzing potential causes of missing scans
        6. Scan Data Transformation Analysis: A visualization showing how data was transformed during cleaning
        7. Hierarchical Missing Scan Heatmap: A heatmap showing missing scans across the hierarchical structure
        8. Missing Scan Patterns by Date and Entity: A heatmap showing missing scan patterns over time
        9. Comparison Visualizations: When both raw and cleaned data are provided, additional visualizations
           showing the differences and improvements

        Args:
            df (pd.DataFrame): Primary DataFrame with scan data (typically cleaned data)
            df_raw (pd.DataFrame, optional): Raw DataFrame before cleaning for comparison
            global_filter (dict, optional): Global filter from treemap selection

        Returns:
            Dict[str, Any]: Dictionary containing generated figures, metrics, tabular data, and comparison data
        """
        try:
            logger.info("Analyzing scan consistency and discrepancies")

            # Initialize comparison flag and return data dictionary
            has_comparison = df_raw is not None
            return_data = {}
            if has_comparison:
                logger.info("Both raw and cleaned data provided - will perform comparison analysis")

            # Apply filtering if specified
            filtered_df = df.copy()
            filtered_df_raw = df_raw.copy() if has_comparison else None
            filter_title = ""

            # Apply global filter if provided (from treemap)
            if global_filter:
                filtered_df, filter_title, entity_type, entity_id = self.apply_global_filter(filtered_df, global_filter)

                # Apply same filter to raw data if available
                if has_comparison:
                    filtered_df_raw, _, _, _ = self.apply_global_filter(filtered_df_raw, global_filter)

                if filtered_df.empty:
                    logger.warning(f"No data available after applying filter: {filter_title}")
                    return {
                        'figures': [go.Figure().add_annotation(
                            text=f"No data available for {entity_type} {entity_id}",
                            xref="paper", yref="paper",
                            x=0.5, y=0.5,
                            showarrow=False,
                            font=dict(size=16)
                        )],
                        'metrics': {},
                        'tabular_data': None,
                        'has_comparison': False
                    }

            # Ensure date column is datetime in both datasets
            if 'date' in filtered_df.columns:
                filtered_df['date'] = pd.to_datetime(filtered_df['date'])

            if has_comparison and 'date' in filtered_df_raw.columns:
                filtered_df_raw['date'] = pd.to_datetime(filtered_df_raw['date'])

            # Log available columns for debugging
            logger.info(f"Available columns in primary DataFrame: {filtered_df.columns.tolist()}")
            if has_comparison:
                logger.info(f"Available columns in raw DataFrame: {filtered_df_raw.columns.tolist()}")

            # Classify scan consistency for primary dataset
            filtered_df = self.classify_scan_consistency(filtered_df)
            metrics = self.calculate_consistency_metrics(filtered_df)

            # Prepare tabular data for export (with all relevant columns)
            tabular_data = filtered_df.copy()

            # Select columns for tabular data, organizing them in a logical hierarchy
            columns_to_keep = ['scan_consistency', 'has_discrepancy']

            # Add hierarchical entity ID columns if available (in order of hierarchy)
            hierarchical_columns = []
            if 'complex_id' in tabular_data.columns:
                hierarchical_columns.append('complex_id')
            if 'building_place_id' in tabular_data.columns:
                hierarchical_columns.append('building_place_id')
            if 'unit_place_id' in tabular_data.columns:
                hierarchical_columns.append('unit_place_id')
            if 'address_id' in tabular_data.columns:
                hierarchical_columns.append('address_id')

            # Add hierarchical columns at the beginning
            columns_to_keep = hierarchical_columns + columns_to_keep

            # Add date column if available
            if 'date' in tabular_data.columns:
                columns_to_keep.append('date')

            # Add scan flag columns
            columns_to_keep.extend(['has_entry', 'has_exit', 'has_delivery'])

            # Add delivery hints if available
            if 'delivery_hints' in tabular_data.columns:
                columns_to_keep.append('delivery_hints')

            # Add coordinate columns in a logical grouping
            coord_groups = [
                ['enter_latitude', 'enter_longitude'],
                ['exit_latitude', 'exit_longitude'],
                ['scan_latitude', 'scan_longitude'],  # Primary delivery scan coordinates
                ['delivery_latitude', 'delivery_longitude'],  # Alternative delivery scan coordinates
                ['deliver_latitude', 'deliver_longitude'],  # Alternative delivery scan coordinates
                ['centroid_latitude', 'centroid_longitude']  # Calculated centroids
            ]

            for group in coord_groups:
                available_cols = [col for col in group if col in tabular_data.columns]
                columns_to_keep.extend(available_cols)

            # Filter to only records with discrepancies and ensure all columns exist
            columns_to_keep = [col for col in columns_to_keep if col in tabular_data.columns]
            discrepancy_data = tabular_data[tabular_data['has_discrepancy']][columns_to_keep]

            # Add a column to indicate the specific discrepancy type for easier filtering
            if not discrepancy_data.empty:
                # Create a more descriptive column for the specific issue
                def get_issue_description(row):
                    if row['scan_consistency'] == 'Entry Only':
                        return 'Missing Exit and Delivery Scans'
                    elif row['scan_consistency'] == 'Exit Only':
                        return 'Missing Entry and Delivery Scans'
                    elif row['scan_consistency'] == 'Delivery Only':
                        return 'Missing Entry and Exit Scans'
                    elif row['scan_consistency'] == 'Missing Entry':
                        return 'Missing Entry Scan Only'
                    elif row['scan_consistency'] == 'Missing Exit':
                        return 'Missing Exit Scan Only'
                    elif row['scan_consistency'] == 'Missing Delivery':
                        return 'Missing Delivery Scan Only'
                    else:
                        return 'Unknown Issue'

                discrepancy_data['issue_description'] = discrepancy_data.apply(get_issue_description, axis=1)

                # Move the issue description column right after scan_consistency
                cols = discrepancy_data.columns.tolist()
                consistency_idx = cols.index('scan_consistency')
                cols.remove('issue_description')
                cols.insert(consistency_idx + 1, 'issue_description')
                discrepancy_data = discrepancy_data[cols]

            # Classify scan consistency for raw dataset if available
            if has_comparison:
                filtered_df_raw = self.classify_scan_consistency(filtered_df_raw)
                metrics_raw = self.calculate_consistency_metrics(filtered_df_raw)

                # Calculate improvements
                improvements = {
                    'discrepancy_rate_change': metrics_raw['discrepancy_rate'] - metrics['discrepancy_rate'],
                    'complete_percent_change': (
                        metrics_raw['consistency_percent'].get('Complete', 0) -
                        metrics['consistency_percent'].get('Complete', 0)
                    ),
                    'all_missing_percent_change': (
                        metrics_raw['consistency_percent'].get('All Missing', 0) -
                        metrics['consistency_percent'].get('All Missing', 0)
                    )
                }

                # Calculate changes for each discrepancy type
                discrepancy_types = [
                    'Entry Only', 'Exit Only', 'Delivery Only',
                    'Missing Entry', 'Missing Exit', 'Missing Delivery'
                ]

                for disc_type in discrepancy_types:
                    raw_pct = metrics_raw['consistency_percent'].get(disc_type, 0)
                    cleaned_pct = metrics['consistency_percent'].get(disc_type, 0)
                    improvements[f'{disc_type.replace(" ", "_").lower()}_change'] = raw_pct - cleaned_pct

                # Add raw metrics and improvements to the metrics dictionary
                metrics['raw'] = metrics_raw
                metrics['improvements'] = improvements

                # Prepare comparison tabular data
                raw_discrepancy_data = filtered_df_raw[filtered_df_raw['has_discrepancy']][columns_to_keep]

                # Add a column to indicate data source
                discrepancy_data['data_source'] = 'Cleaned'
                raw_discrepancy_data['data_source'] = 'Raw'

                # Combine for comparison
                combined_discrepancy_data = pd.concat([discrepancy_data, raw_discrepancy_data])

                # Use the combined data for the tabular visualization
                tabular_data = combined_discrepancy_data
            else:
                # Use only the discrepancy data for the tabular visualization
                tabular_data = discrepancy_data

            # Create list to store figures
            figures = []

            # 1. Create Enhanced Consistency Distribution Visualization
            try:
                # Define the categories in a logical order
                categories = [
                    'Complete', 'All Missing',  # Ideal states
                    'Missing Entry', 'Missing Exit', 'Missing Delivery',  # Missing one scan
                    'Entry Only', 'Exit Only', 'Delivery Only'  # Only one scan present
                ]

                # Get values for each category
                values = [metrics['consistency_counts'].get(cat, 0) for cat in categories]
                percentages = [metrics['consistency_percent'].get(cat, 0) for cat in categories]

                # Define colors for each category
                color_map = {
                    'Complete': '#28a745',  # Green
                    'All Missing': '#6c757d',  # Gray
                    'Entry Only': '#007bff',  # Blue
                    'Exit Only': '#17a2b8',  # Cyan
                    'Delivery Only': '#dc3545',  # Red
                    'Missing Entry': '#fd7e14',  # Orange
                    'Missing Exit': '#ffc107',  # Yellow
                    'Missing Delivery': '#e83e8c',  # Pink
                    'Unknown': '#6610f2'  # Purple
                }

                # Create color list in the same order as categories
                colors = [color_map.get(cat, '#6c757d') for cat in categories]

                # Create a combined visualization with bar chart and detailed information
                fig_consistency = go.Figure()

                # Add horizontal bar chart
                fig_consistency.add_trace(go.Bar(
                    y=categories,
                    x=values,
                    orientation='h',
                    marker_color=colors,
                    text=[f"{p:.1f}%" for p in percentages],
                    textposition='auto',
                    hovertemplate='<b>%{y}</b><br>Count: %{x}<br>Percentage: %{text}<extra></extra>'
                ))

                # Add annotations with detailed descriptions for each category
                # Create a dictionary mapping categories to their descriptions
                # This information is used in the hover text and summary annotation
                category_descriptions = {
                    'Complete': 'All three scan types present (ideal)',
                    'All Missing': 'No scan data available',
                    'Missing Entry': 'Exit and delivery scans present, entry scan missing',
                    'Missing Exit': 'Entry and delivery scans present, exit scan missing',
                    'Missing Delivery': 'Entry and exit scans present, delivery scan missing',
                    'Entry Only': 'Only entry scan present, exit and delivery missing',
                    'Exit Only': 'Only exit scan present, entry and delivery missing',
                    'Delivery Only': 'Only delivery scan present, entry and exit missing'
                }

                # Prepare detailed hover data with counts for each scan type
                hover_data = []
                for i, category in enumerate(categories):
                    # Get the subset of data for this category
                    category_data = filtered_df[filtered_df['scan_consistency'] == category]

                    # Calculate counts for each scan type
                    entry_count = category_data['has_entry_scan'].sum() if 'has_entry_scan' in category_data.columns else 0
                    exit_count = category_data['has_exit_scan'].sum() if 'has_exit_scan' in category_data.columns else 0
                    delivery_count = category_data['has_delivery_scan'].sum() if 'has_delivery_scan' in category_data.columns else 0

                    # Calculate missing counts
                    missing_entry = len(category_data) - entry_count
                    missing_exit = len(category_data) - exit_count
                    missing_delivery = len(category_data) - delivery_count

                    # Add to hover data
                    hover_data.append([
                        values[i],  # Total count
                        percentages[i],  # Percentage
                        entry_count,
                        exit_count,
                        delivery_count,
                        missing_entry,
                        missing_exit,
                        missing_delivery,
                        category_descriptions.get(category, '')  # Description
                    ])

                # Update hover template to include detailed information
                fig_consistency.update_traces(
                    customdata=hover_data,
                    hovertemplate=(
                        '<b>%{y}</b><br>' +
                        'Count: %{customdata[0]}<br>' +
                        'Percentage: %{customdata[1]:.1f}%<br>' +
                        '<b>Present Scans:</b><br>' +
                        'Entry: %{customdata[2]}<br>' +
                        'Exit: %{customdata[3]}<br>' +
                        'Delivery: %{customdata[4]}<br>' +
                        '<b>Missing Scans:</b><br>' +
                        'Entry: %{customdata[5]}<br>' +
                        'Exit: %{customdata[6]}<br>' +
                        'Delivery: %{customdata[7]}<br>' +
                        '<b>Description:</b> %{customdata[8]}<br>' +
                        '<extra></extra>'
                    )
                )

                # Add comparison with raw data if available
                if has_comparison:
                    # Get raw values for each category
                    raw_values = [metrics_raw['consistency_counts'].get(cat, 0) for cat in categories]
                    raw_percentages = [metrics_raw['consistency_percent'].get(cat, 0) for cat in categories]

                    # Prepare detailed hover data for raw data
                    raw_hover_data = []
                    for i, category in enumerate(categories):
                        # Get the subset of data for this category
                        category_data = filtered_df_raw[filtered_df_raw['scan_consistency'] == category]

                        # Calculate counts for each scan type
                        entry_count = category_data['has_entry_scan'].sum() if 'has_entry_scan' in category_data.columns else 0
                        exit_count = category_data['has_exit_scan'].sum() if 'has_exit_scan' in category_data.columns else 0
                        delivery_count = category_data['has_delivery_scan'].sum() if 'has_delivery_scan' in category_data.columns else 0

                        # Calculate missing counts
                        missing_entry = len(category_data) - entry_count
                        missing_exit = len(category_data) - exit_count
                        missing_delivery = len(category_data) - delivery_count

                        # Calculate improvements
                        count_improvement = values[i] - raw_values[i]
                        pct_improvement = percentages[i] - raw_percentages[i]

                        # Add to hover data
                        raw_hover_data.append([
                            raw_values[i],  # Total count (before)
                            raw_percentages[i],  # Percentage (before)
                            entry_count,
                            exit_count,
                            delivery_count,
                            missing_entry,
                            missing_exit,
                            missing_delivery,
                            category_descriptions.get(category, ''),  # Description
                            count_improvement,  # Count improvement
                            pct_improvement  # Percentage improvement
                        ])

                    # Add a second bar chart for raw data (before cleaning) with enhanced hover
                    fig_consistency.add_trace(go.Bar(
                        y=categories,
                        x=raw_values,
                        orientation='h',
                        marker_color=[color_map.get(cat, '#6c757d') for cat in categories],
                        marker=dict(
                            color=[color_map.get(cat, '#6c757d') for cat in categories],
                            opacity=0.5,  # Make it semi-transparent
                            line=dict(color='black', width=1)  # Add outline
                        ),
                        text=[f"{p:.1f}%" for p in raw_percentages],
                        textposition='auto',
                        name='Before Cleaning',
                        customdata=raw_hover_data,
                        hovertemplate=(
                            '<b>%{y}</b> (Before Cleaning)<br>' +
                            'Count: %{customdata[0]}<br>' +
                            'Percentage: %{customdata[1]:.1f}%<br>' +
                            '<b>Present Scans:</b><br>' +
                            'Entry: %{customdata[2]}<br>' +
                            'Exit: %{customdata[3]}<br>' +
                            'Delivery: %{customdata[4]}<br>' +
                            '<b>Missing Scans:</b><br>' +
                            'Entry: %{customdata[5]}<br>' +
                            'Exit: %{customdata[6]}<br>' +
                            'Delivery: %{customdata[7]}<br>' +
                            '<b>Description:</b> %{customdata[8]}<br>' +
                            '<b>Improvement After Cleaning:</b><br>' +
                            'Count: %{customdata[9]}<br>' +
                            'Percentage: %{customdata[10]:.1f}%<br>' +
                            '<extra></extra>'
                        )
                    ))

                    # Update the name of the first trace
                    fig_consistency.data[0].name = 'After Cleaning'

                    # Update layout for comparison
                    fig_consistency.update_layout(
                        barmode='overlay',
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                # Calculate total records with discrepancies
                discrepancy_count = sum([values[i] for i in range(len(categories)) if categories[i] not in ['Complete', 'All Missing']])
                discrepancy_percent = sum([percentages[i] for i in range(len(categories)) if categories[i] not in ['Complete', 'All Missing']])

                # Add a summary annotation
                if has_comparison:
                    # Calculate raw discrepancy metrics
                    raw_discrepancy_count = sum([raw_values[i] for i in range(len(categories)) if categories[i] not in ['Complete', 'All Missing']])
                    raw_discrepancy_percent = sum([raw_percentages[i] for i in range(len(categories)) if categories[i] not in ['Complete', 'All Missing']])

                    # Calculate improvement
                    discrepancy_improvement = raw_discrepancy_percent - discrepancy_percent
                    improvement_text = f"↓ {discrepancy_improvement:.1f}%" if discrepancy_improvement > 0 else f"↑ {abs(discrepancy_improvement):.1f}%"
                    improvement_color = "green" if discrepancy_improvement > 0 else "red"

                    fig_consistency.add_annotation(
                        x=0.5,
                        y=1.15,
                        xref="paper",
                        yref="paper",
                        text=f"<b>Scan Consistency Overview</b><br>" +
                             f"Before Cleaning: Records with Discrepancies: {raw_discrepancy_count:,} ({raw_discrepancy_percent:.1f}%)<br>" +
                             f"After Cleaning: Records with Discrepancies: {discrepancy_count:,} ({discrepancy_percent:.1f}%)<br>" +
                             f"Improvement: <span style='color:{improvement_color};'>{improvement_text}</span>",
                        showarrow=False,
                        font=dict(size=14),
                        align="center",
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="#4a90e2",
                        borderwidth=2,
                        borderpad=6
                    )
                else:
                    fig_consistency.add_annotation(
                        x=0.5,
                        y=1.12,
                        xref="paper",
                        yref="paper",
                        text=f"<b>Scan Consistency Overview</b><br>Total Records: {metrics['total_records']:,} | Records with Discrepancies: {discrepancy_count:,} ({discrepancy_percent:.1f}%)",
                        showarrow=False,
                        font=dict(size=14),
                        align="center",
                        bgcolor="rgba(255, 255, 255, 0.8)",
                        bordercolor="#4a90e2",
                        borderwidth=2,
                        borderpad=4
                    )

                # Update layout
                fig_consistency.update_layout(
                    title=f"Scan Consistency Distribution{filter_title}",
                    xaxis_title="Number of Records",
                    yaxis_title="Consistency Category",
                    template=self.plot_template,
                    margin=dict(l=20, r=20, t=100, b=20),
                    height=500,
                    yaxis=dict(
                        categoryorder='array',
                        categoryarray=categories[::-1]  # Reverse to show Complete at the top
                    )
                )

                # Set consistent size
                self.set_consistent_size(fig_consistency)

                # Add to figures list
                figures.append(fig_consistency)
            except Exception as e:
                logger.error(f"Error creating enhanced consistency distribution: {str(e)}", exc_info=True)

            # Removed "Discrepancy by Time of Day Heatmap" as per requirements

            # Removed "Top Problematic Buildings Bar Chart" as per requirements

            # Removed "Discrepancy Types Distribution" as per requirements

            # Define hierarchical levels for use in new visualizations
            hierarchical_levels = []
            if 'complex_id' in filtered_df.columns:
                hierarchical_levels.append('complex_id')
            if 'building_place_id' in filtered_df.columns:
                hierarchical_levels.append('building_place_id')
            if 'unit_place_id' in filtered_df.columns:
                hierarchical_levels.append('unit_place_id')
            if 'address_id' in filtered_df.columns:
                hierarchical_levels.append('address_id')

            # Removed "Unified Hierarchical Analysis" as per requirements



            # Removed "Unified Temporal and Spatial Analysis" as per requirements

            # Create an enhanced comprehensive summary table
            try:
                # Get consistency categories in a logical order
                categories = ['Complete', 'All Missing', 'Entry Only', 'Exit Only', 'Delivery Only',
                             'Missing Entry', 'Missing Exit', 'Missing Delivery']

                # Create summary data with both scan types and consistency categories
                summary_data = {
                    'Category': ['Entry Scans', 'Exit Scans', 'Delivery Scans',
                                '---', # Separator
                                'Complete Records', 'All Missing Records',
                                'Entry Only', 'Exit Only', 'Delivery Only',
                                'Missing Entry', 'Missing Exit', 'Missing Delivery'],
                    'Count': [
                        metrics['total_records'] - metrics['missing_entry_count'],
                        metrics['total_records'] - metrics['missing_exit_count'],
                        metrics['total_records'] - metrics['missing_delivery_count'],
                        None, # Separator
                        metrics['consistency_counts'].get('Complete', 0),
                        metrics['consistency_counts'].get('All Missing', 0),
                        metrics['consistency_counts'].get('Entry Only', 0),
                        metrics['consistency_counts'].get('Exit Only', 0),
                        metrics['consistency_counts'].get('Delivery Only', 0),
                        metrics['consistency_counts'].get('Missing Entry', 0),
                        metrics['consistency_counts'].get('Missing Exit', 0),
                        metrics['consistency_counts'].get('Missing Delivery', 0)
                    ],
                    'Missing Count': [
                        metrics['missing_entry_count'],
                        metrics['missing_exit_count'],
                        metrics['missing_delivery_count'],
                        None, # Separator
                        None, None, None, None, None, None, None, None
                    ],
                    'Total Records': [
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records'],
                        None, # Separator
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records'],
                        metrics['total_records']
                    ],
                    'Percentage': [
                        100 - metrics['missing_entry_percent'],
                        100 - metrics['missing_exit_percent'],
                        100 - metrics['missing_delivery_percent'],
                        None, # Separator
                        metrics['consistency_percent'].get('Complete', 0),
                        metrics['consistency_percent'].get('All Missing', 0),
                        metrics['consistency_percent'].get('Entry Only', 0),
                        metrics['consistency_percent'].get('Exit Only', 0),
                        metrics['consistency_percent'].get('Delivery Only', 0),
                        metrics['consistency_percent'].get('Missing Entry', 0),
                        metrics['consistency_percent'].get('Missing Exit', 0),
                        metrics['consistency_percent'].get('Missing Delivery', 0)
                    ],
                    'Missing Percentage': [
                        metrics['missing_entry_percent'],
                        metrics['missing_exit_percent'],
                        metrics['missing_delivery_percent'],
                        None, # Separator
                        None, None, None, None, None, None, None, None
                    ]
                }

                # Create a DataFrame for the summary data
                summary_df = pd.DataFrame(summary_data)

                # Define color mapping for different categories
                color_map = {
                    'Complete Records': '#28a745',  # Green
                    'All Missing Records': '#6c757d',  # Gray
                    'Entry Only': '#007bff',  # Blue
                    'Exit Only': '#17a2b8',  # Cyan
                    'Delivery Only': '#dc3545',  # Red
                    'Missing Entry': '#fd7e14',  # Orange
                    'Missing Exit': '#ffc107',  # Yellow
                    'Missing Delivery': '#e83e8c',  # Pink
                    'Entry Scans': '#007bff',  # Blue
                    'Exit Scans': '#17a2b8',  # Cyan
                    'Delivery Scans': '#dc3545'  # Red
                }

                # Create background colors for the category column
                category_colors = []
                for cat in summary_df['Category']:
                    if cat == '---':
                        category_colors.append('#f8f9fa')  # Light gray for separator
                    else:
                        category_colors.append(color_map.get(cat, '#f8f9fa'))

                # Create a table figure with enhanced styling
                fig_summary = go.Figure(data=[go.Table(
                    header=dict(
                        values=['Category', 'Present Count', 'Missing Count', 'Total Records', 'Present %', 'Missing %'],
                        fill_color='#4a90e2',
                        align='left',
                        font=dict(color='white', size=14)
                    ),
                    cells=dict(
                        values=[
                            summary_df['Category'],
                            summary_df['Count'],
                            summary_df['Missing Count'],
                            summary_df['Total Records'],
                            [f"{val:.1f}%" if val is not None else "" for val in summary_df['Percentage']],
                            [f"{val:.1f}%" if val is not None else "" for val in summary_df['Missing Percentage']]
                        ],
                        fill_color=[
                            [color if i != 3 else '#e0e0e0' for i, color in enumerate(category_colors)],  # Category colors with separator
                            ['#f8f9fa' if i != 3 else '#e0e0e0' for i in range(len(summary_df))],  # Present count
                            [
                                '#ffcdd2' if i == 0 and metrics['missing_entry_percent'] > 10 else
                                '#ffcdd2' if i == 1 and metrics['missing_exit_percent'] > 10 else
                                '#ffcdd2' if i == 2 and metrics['missing_delivery_percent'] > 10 else
                                '#e0e0e0' if i == 3 else '#f8f9fa'
                                for i in range(len(summary_df))
                            ],  # Missing count
                            ['#f8f9fa' if i != 3 else '#e0e0e0' for i in range(len(summary_df))],  # Total records
                            ['#f8f9fa' if i != 3 else '#e0e0e0' for i in range(len(summary_df))],  # Present percentage
                            [
                                '#ffcdd2' if i == 0 and metrics['missing_entry_percent'] > 10 else
                                '#ffcdd2' if i == 1 and metrics['missing_exit_percent'] > 10 else
                                '#ffcdd2' if i == 2 and metrics['missing_delivery_percent'] > 10 else
                                '#e0e0e0' if i == 3 else '#f8f9fa'
                                for i in range(len(summary_df))
                            ]  # Missing percentage
                        ],
                        align='left',
                        font=dict(size=13),
                        height=30,
                        line=dict(color='#e0e0e0', width=1)
                    )
                )])

                # Update layout
                fig_summary.update_layout(
                    title=f"Comprehensive Scan Consistency Summary{filter_title}",
                    margin=dict(l=20, r=20, t=50, b=20),
                    height=400
                )

                # Add to figures list - put it at the beginning for prominence
                figures.insert(0, fig_summary)
            except Exception as e:
                logger.error(f"Error creating enhanced summary table: {str(e)}", exc_info=True)

            # Removed "Comparison Visualizations" as per requirements

            # Create Root Cause Analysis Visualization
            try:
                # Analyze potential causes of missing scans

                # Check for empty coordinates (null values)
                empty_coords_entry = filtered_df[(~filtered_df['has_entry']) & (filtered_df['enter_latitude'].isna() | filtered_df['enter_longitude'].isna())].shape[0]
                empty_coords_exit = filtered_df[(~filtered_df['has_exit']) & (filtered_df['exit_latitude'].isna() | filtered_df['exit_longitude'].isna())].shape[0]
                empty_coords_delivery = filtered_df[(~filtered_df['has_delivery']) & (filtered_df['scan_latitude'].isna() | filtered_df['scan_longitude'].isna())].shape[0]

                # Check for out-of-bounds coordinates (coordinates that exist but are invalid)
                out_of_bounds_entry = filtered_df[(~filtered_df['has_entry']) & (~filtered_df['enter_latitude'].isna()) & (~filtered_df['enter_longitude'].isna())].shape[0]
                out_of_bounds_exit = filtered_df[(~filtered_df['has_exit']) & (~filtered_df['exit_latitude'].isna()) & (~filtered_df['exit_longitude'].isna())].shape[0]
                out_of_bounds_delivery = filtered_df[(~filtered_df['has_delivery']) & (~filtered_df['scan_latitude'].isna()) & (~filtered_df['scan_longitude'].isna())].shape[0]

                # Calculate other/unknown causes
                other_entry = metrics['missing_entry_count'] - empty_coords_entry - out_of_bounds_entry
                other_exit = metrics['missing_exit_count'] - empty_coords_exit - out_of_bounds_exit
                other_delivery = metrics['missing_delivery_count'] - empty_coords_delivery - out_of_bounds_delivery

                # Create cause data
                cause_data = {
                    'Cause': ['Empty Coordinates', 'Out-of-Bounds Coordinates', 'Other/Unknown'],
                    'Missing Entry': [empty_coords_entry, out_of_bounds_entry, other_entry],
                    'Missing Exit': [empty_coords_exit, out_of_bounds_exit, other_exit],
                    'Missing Delivery': [empty_coords_delivery, out_of_bounds_delivery, other_delivery]
                }

                # Create DataFrame
                cause_df = pd.DataFrame(cause_data)

                # Create stacked bar chart
                fig_causes = go.Figure()

                # Add bars for each scan type
                for scan_type, color in [('Missing Entry', '#007bff'), ('Missing Exit', '#17a2b8'), ('Missing Delivery', '#dc3545')]:
                    fig_causes.add_trace(go.Bar(
                        x=cause_df['Cause'],
                        y=cause_df[scan_type],
                        name=scan_type,
                        marker_color=color,
                        hovertemplate='%{x}<br>%{y} records<br>%{fullData.name}<extra></extra>'
                    ))

                # Add comparison with raw data if available
                if has_comparison:
                    # Check for empty coordinates in raw data
                    empty_coords_entry_raw = filtered_df_raw[(~filtered_df_raw['has_entry']) & (filtered_df_raw['enter_latitude'].isna() | filtered_df_raw['enter_longitude'].isna())].shape[0]
                    empty_coords_exit_raw = filtered_df_raw[(~filtered_df_raw['has_exit']) & (filtered_df_raw['exit_latitude'].isna() | filtered_df_raw['exit_longitude'].isna())].shape[0]
                    empty_coords_delivery_raw = filtered_df_raw[(~filtered_df_raw['has_delivery']) & (filtered_df_raw['scan_latitude'].isna() | filtered_df_raw['scan_longitude'].isna())].shape[0]

                    # Check for out-of-bounds coordinates in raw data
                    out_of_bounds_entry_raw = filtered_df_raw[(~filtered_df_raw['has_entry']) & (~filtered_df_raw['enter_latitude'].isna()) & (~filtered_df_raw['enter_longitude'].isna())].shape[0]
                    out_of_bounds_exit_raw = filtered_df_raw[(~filtered_df_raw['has_exit']) & (~filtered_df_raw['exit_latitude'].isna()) & (~filtered_df_raw['exit_longitude'].isna())].shape[0]
                    out_of_bounds_delivery_raw = filtered_df_raw[(~filtered_df_raw['has_delivery']) & (~filtered_df_raw['scan_latitude'].isna()) & (~filtered_df_raw['scan_longitude'].isna())].shape[0]

                    # Calculate other/unknown causes in raw data
                    other_entry_raw = metrics_raw['missing_entry_count'] - empty_coords_entry_raw - out_of_bounds_entry_raw
                    other_exit_raw = metrics_raw['missing_exit_count'] - empty_coords_exit_raw - out_of_bounds_exit_raw
                    other_delivery_raw = metrics_raw['missing_delivery_count'] - empty_coords_delivery_raw - out_of_bounds_delivery_raw

                    # Create cause data for raw data
                    cause_data_raw = {
                        'Cause': ['Empty Coordinates', 'Out-of-Bounds Coordinates', 'Other/Unknown'],
                        'Missing Entry (Raw)': [empty_coords_entry_raw, out_of_bounds_entry_raw, other_entry_raw],
                        'Missing Exit (Raw)': [empty_coords_exit_raw, out_of_bounds_exit_raw, other_exit_raw],
                        'Missing Delivery (Raw)': [empty_coords_delivery_raw, out_of_bounds_delivery_raw, other_delivery_raw]
                    }

                    # Create DataFrame for raw data
                    cause_df_raw = pd.DataFrame(cause_data_raw)

                    # Add bars for each scan type in raw data (with hatched pattern)
                    for scan_type, color in [('Missing Entry (Raw)', '#007bff'), ('Missing Exit (Raw)', '#17a2b8'), ('Missing Delivery (Raw)', '#dc3545')]:
                        fig_causes.add_trace(go.Bar(
                            x=cause_df_raw['Cause'],
                            y=cause_df_raw[scan_type],
                            name=scan_type.replace(' (Raw)', ' (Before)'),
                            marker=dict(
                                color=color,
                                opacity=0.5,
                                line=dict(color='black', width=1)
                            ),
                            hovertemplate='%{x}<br>%{y} records<br>%{fullData.name}<extra></extra>'
                        ))

                    # Update names of original traces
                    for i in range(3):
                        fig_causes.data[i].name = fig_causes.data[i].name + ' (After)'

                # Add a summary annotation
                total_missing = metrics['missing_entry_count'] + metrics['missing_exit_count'] + metrics['missing_delivery_count']
                empty_coords_total = empty_coords_entry + empty_coords_exit + empty_coords_delivery
                out_of_bounds_total = out_of_bounds_entry + out_of_bounds_exit + out_of_bounds_delivery
                other_total = other_entry + other_exit + other_delivery

                empty_coords_pct = (empty_coords_total / total_missing) * 100 if total_missing > 0 else 0
                out_of_bounds_pct = (out_of_bounds_total / total_missing) * 100 if total_missing > 0 else 0
                other_pct = (other_total / total_missing) * 100 if total_missing > 0 else 0

                fig_causes.add_annotation(
                    x=0.5,
                    y=1.15,
                    xref="paper",
                    yref="paper",
                    text=f"<b>Root Causes of Missing Scans</b><br>" +
                         f"Empty Coordinates: {empty_coords_total:,} ({empty_coords_pct:.1f}%)<br>" +
                         f"Out-of-Bounds Coordinates: {out_of_bounds_total:,} ({out_of_bounds_pct:.1f}%)<br>" +
                         f"Other/Unknown: {other_total:,} ({other_pct:.1f}%)",
                    showarrow=False,
                    font=dict(size=14),
                    align="center",
                    bgcolor="rgba(255, 255, 255, 0.9)",
                    bordercolor="#4a90e2",
                    borderwidth=2,
                    borderpad=6
                )

                # Update layout
                fig_causes.update_layout(
                    title=f"Root Causes of Missing Scans{filter_title}",
                    xaxis_title="Potential Cause",
                    yaxis_title="Number of Records",
                    barmode='group',
                    template=self.plot_template,
                    legend=dict(
                        orientation="h",
                        yanchor="bottom",
                        y=1.02,
                        xanchor="right",
                        x=1
                    )
                )

                # Set consistent size
                self.set_consistent_size(fig_causes)

                # Add to figures list
                figures.append(fig_causes)
            except Exception as e:
                logger.error(f"Error creating root cause analysis: {str(e)}", exc_info=True)

            # Create Scan Data Transformation Analysis
            if has_comparison:
                try:
                    # Calculate transformation metrics

                    # Calculate filled values (values that were missing in raw data but present in cleaned data)
                    filled_entry = filtered_df_raw[filtered_df_raw['enter_latitude'].isna() | filtered_df_raw['enter_longitude'].isna()].shape[0] - filtered_df[filtered_df['enter_latitude'].isna() | filtered_df['enter_longitude'].isna()].shape[0]
                    filled_exit = filtered_df_raw[filtered_df_raw['exit_latitude'].isna() | filtered_df_raw['exit_longitude'].isna()].shape[0] - filtered_df[filtered_df['exit_latitude'].isna() | filtered_df['exit_longitude'].isna()].shape[0]
                    filled_delivery = filtered_df_raw[filtered_df_raw['scan_latitude'].isna() | filtered_df_raw['scan_longitude'].isna()].shape[0] - filtered_df[filtered_df['scan_latitude'].isna() | filtered_df['scan_longitude'].isna()].shape[0]

                    # Calculate corrected values (values that were present but invalid in raw data and corrected in cleaned data)
                    corrected_entry = filtered_df_raw[(~filtered_df_raw['has_entry']) & (~filtered_df_raw['enter_latitude'].isna()) & (~filtered_df_raw['enter_longitude'].isna())].shape[0] - filtered_df[(~filtered_df['has_entry']) & (~filtered_df['enter_latitude'].isna()) & (~filtered_df['enter_longitude'].isna())].shape[0]
                    corrected_exit = filtered_df_raw[(~filtered_df_raw['has_exit']) & (~filtered_df_raw['exit_latitude'].isna()) & (~filtered_df_raw['exit_longitude'].isna())].shape[0] - filtered_df[(~filtered_df['has_exit']) & (~filtered_df['exit_latitude'].isna()) & (~filtered_df['exit_longitude'].isna())].shape[0]
                    corrected_delivery = filtered_df_raw[(~filtered_df_raw['has_delivery']) & (~filtered_df_raw['scan_latitude'].isna()) & (~filtered_df_raw['scan_longitude'].isna())].shape[0] - filtered_df[(~filtered_df['has_delivery']) & (~filtered_df['scan_latitude'].isna()) & (~filtered_df['scan_longitude'].isna())].shape[0]

                    # Calculate remaining missing values (values that are still missing after cleaning)
                    remaining_entry = filtered_df[~filtered_df['has_entry']].shape[0]
                    remaining_exit = filtered_df[~filtered_df['has_exit']].shape[0]
                    remaining_delivery = filtered_df[~filtered_df['has_delivery']].shape[0]

                    # Create transformation data
                    transformation_data = {
                        'Transformation': ['Filled Values', 'Corrected Values', 'Remaining Missing'],
                        'Entry Scans': [filled_entry, corrected_entry, remaining_entry],
                        'Exit Scans': [filled_exit, corrected_exit, remaining_exit],
                        'Delivery Scans': [filled_delivery, corrected_delivery, remaining_delivery]
                    }

                    # Create DataFrame
                    transformation_df = pd.DataFrame(transformation_data)

                    # Create stacked bar chart
                    fig_transform = go.Figure()

                    # Add bars for each scan type
                    for scan_type, color in [('Entry Scans', '#007bff'), ('Exit Scans', '#17a2b8'), ('Delivery Scans', '#dc3545')]:
                        fig_transform.add_trace(go.Bar(
                            x=transformation_df['Transformation'],
                            y=transformation_df[scan_type],
                            name=scan_type,
                            marker_color=color,
                            hovertemplate='%{x}<br>%{y} records<br>%{fullData.name}<extra></extra>'
                        ))

                    # Add a summary annotation
                    total_filled = filled_entry + filled_exit + filled_delivery
                    total_corrected = corrected_entry + corrected_exit + corrected_delivery
                    total_remaining = remaining_entry + remaining_exit + remaining_delivery

                    total_raw_missing = metrics_raw['missing_entry_count'] + metrics_raw['missing_exit_count'] + metrics_raw['missing_delivery_count']
                    total_cleaned_missing = metrics['missing_entry_count'] + metrics['missing_exit_count'] + metrics['missing_delivery_count']

                    improvement_pct = ((total_raw_missing - total_cleaned_missing) / total_raw_missing) * 100 if total_raw_missing > 0 else 0

                    fig_transform.add_annotation(
                        x=0.5,
                        y=1.15,
                        xref="paper",
                        yref="paper",
                        text=f"<b>Data Transformation Analysis</b><br>" +
                             f"Total Filled Values: {total_filled:,}<br>" +
                             f"Total Corrected Values: {total_corrected:,}<br>" +
                             f"Total Remaining Missing: {total_remaining:,}<br>" +
                             f"Overall Improvement: {improvement_pct:.1f}%",
                        showarrow=False,
                        font=dict(size=14),
                        align="center",
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="#4a90e2",
                        borderwidth=2,
                        borderpad=6
                    )

                    # Update layout
                    fig_transform.update_layout(
                        title=f"Scan Data Transformation Analysis{filter_title}",
                        xaxis_title="Transformation Type",
                        yaxis_title="Number of Records",
                        barmode='group',
                        template=self.plot_template,
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                    # Set consistent size
                    self.set_consistent_size(fig_transform)

                    # Add to figures list
                    figures.append(fig_transform)
                except Exception as e:
                    logger.error(f"Error creating transformation analysis: {str(e)}", exc_info=True)

            # Removed "Detailed Records with Scan Discrepancies" visualization as per requirements

            # Add new visualization for filled values during cleaning
            if has_comparison and df_raw is not None:
                try:
                    # First, identify records with discrepancies (same as for discrepancy data)
                    # This ensures we're working with the same subset of records
                    raw_with_discrepancy = df_raw.copy()
                    raw_with_discrepancy = self.classify_scan_consistency(raw_with_discrepancy)

                    # Filter to only include records with discrepancies
                    discrepancy_mask = raw_with_discrepancy['has_discrepancy']
                    filled_values_df = raw_with_discrepancy[discrepancy_mask].copy()

                    # Check for filled values in coordinate columns
                    coordinate_columns = [
                        'enter_latitude', 'enter_longitude',
                        'exit_latitude', 'exit_longitude',
                        'scan_latitude', 'scan_longitude'
                    ]

                    # Track counts for summary
                    filled_counts = {col: 0 for col in coordinate_columns}
                    total_filled = 0

                    # Track which cells were filled (for visualization purposes)
                    filled_cells_info = []

                    # Fill in the values that were added during cleaning
                    for col in coordinate_columns:
                        if col in df.columns and col in filled_values_df.columns:
                            # For each record with discrepancy, find cells where value was NaN in raw data but has a value in cleaned data
                            for idx in filled_values_df.index:
                                if idx in df.index:  # Make sure the index exists in both dataframes
                                    if pd.isna(filled_values_df.loc[idx, col]) and not pd.isna(df.loc[idx, col]):
                                        # Fill the empty cell with the value from cleaned data
                                        filled_values_df.loc[idx, col] = df.loc[idx, col]

                                        # Track that this cell was filled
                                        filled_counts[col] += 1
                                        total_filled += 1

                                        # Track which cell was filled for visualization
                                        filled_cells_info.append({
                                            'row_index': idx,
                                            'column': col,
                                            'filled_value': df.loc[idx, col]
                                        })

                    # Add a column to indicate this is filled values data
                    filled_values_df['data_source'] = 'Filled'

                    # Create a summary figure for filled values
                    fig_filled_summary = go.Figure()

                    # Add a bar chart showing filled values by field
                    fields = [col for col in coordinate_columns if filled_counts[col] > 0]
                    counts = [filled_counts[col] for col in coordinate_columns if filled_counts[col] > 0]

                    if fields and counts:  # Only create the chart if we have data
                        # Create readable labels
                        labels = [col.replace('_', ' ').title() for col in fields]

                        # Add bar chart
                        fig_filled_summary.add_trace(go.Bar(
                            x=labels,
                            y=counts,
                            marker_color='#4a90e2',
                            text=counts,
                            textposition='auto',
                            hovertemplate='%{x}<br>Filled Values: %{y}<extra></extra>'
                        ))

                        # Update layout
                        fig_filled_summary.update_layout(
                            title=f"Filled Values During Cleaning{filter_title}",
                            xaxis_title="Field",
                            yaxis_title="Number of Values Filled",
                            template=self.plot_template,
                            margin=dict(l=20, r=20, t=80, b=20)
                        )

                        # Add a summary annotation
                        fig_filled_summary.add_annotation(
                            x=0.5,
                            y=1.15,
                            xref="paper",
                            yref="paper",
                            text=f"<b>Filled Values Summary</b><br>Total Values Filled: {total_filled:,}",
                            showarrow=False,
                            font=dict(size=14),
                            align="center",
                            bgcolor="rgba(255, 255, 255, 0.9)",
                            bordercolor="#4a90e2",
                            borderwidth=2,
                            borderpad=6
                        )

                        # Set consistent size
                        self.set_consistent_size(fig_filled_summary)

                        # Add to figures list
                        figures.append(fig_filled_summary)

                    # Store the filled values data for export
                    result_data = {
                        'filled_values_data': filled_values_df.to_dict('records')
                    }

                    # Add to return data
                    return_data.update(result_data)

                    # Log success
                    logger.info(f"Successfully created filled values visualization with {total_filled} filled values")

                except Exception as e:
                    logger.error(f"Error creating filled values visualization: {str(e)}", exc_info=True)

            # Create Hierarchical Missing Scan Heatmap
            try:
                # Check if we have hierarchical data
                hierarchical_levels = []
                if 'complex_id' in filtered_df.columns:
                    hierarchical_levels.append('complex_id')
                if 'building_place_id' in filtered_df.columns:
                    hierarchical_levels.append('building_place_id')
                if 'unit_place_id' in filtered_df.columns:
                    hierarchical_levels.append('unit_place_id')
                if 'address_id' in filtered_df.columns:
                    hierarchical_levels.append('address_id')

                if len(hierarchical_levels) >= 2:  # Need at least 2 levels for a meaningful heatmap
                    # Choose the most appropriate levels based on data distribution
                    # Start with the first two levels
                    level_x = hierarchical_levels[0]  # Higher level (e.g., complex_id)
                    level_y = hierarchical_levels[1]  # Lower level (e.g., building_place_id)

                    # Check if we have too many unique values in level_y
                    unique_y_values = filtered_df[level_y].nunique()
                    if unique_y_values > 30 and len(hierarchical_levels) > 2:
                        # If too many values in level_y, use a higher level of aggregation
                        level_x = hierarchical_levels[0]
                        level_y = hierarchical_levels[1]
                    elif unique_y_values < 5 and len(hierarchical_levels) > 2:
                        # If too few values in level_y, use a lower level of aggregation
                        level_y = hierarchical_levels[2]

                    # Calculate missing scan percentages for each combination of levels
                    missing_scan_data = []

                    # Group by both levels
                    grouped = filtered_df.groupby([level_x, level_y])

                    for (x_id, y_id), group in grouped:
                        # Skip if either ID is None or NaN
                        if pd.isna(x_id) or pd.isna(y_id) or x_id is None or y_id is None:
                            continue

                        # Calculate missing scan percentages
                        total = len(group)
                        missing_entry_pct = (group[~group['has_entry']].shape[0] / total) * 100 if total > 0 else 0
                        missing_exit_pct = (group[~group['has_exit']].shape[0] / total) * 100 if total > 0 else 0
                        missing_delivery_pct = (group[~group['has_delivery']].shape[0] / total) * 100 if total > 0 else 0

                        # Calculate average missing percentage
                        avg_missing_pct = (missing_entry_pct + missing_exit_pct + missing_delivery_pct) / 3

                        # Store data
                        missing_scan_data.append({
                            level_x: x_id,
                            level_y: y_id,
                            'missing_entry_pct': missing_entry_pct,
                            'missing_exit_pct': missing_exit_pct,
                            'missing_delivery_pct': missing_delivery_pct,
                            'avg_missing_pct': avg_missing_pct,
                            'total_records': total
                        })

                    # Convert to DataFrame
                    if missing_scan_data:
                        missing_scan_df = pd.DataFrame(missing_scan_data)

                        # Limit the number of values on each axis to improve readability
                        # Get top values by average missing percentage
                        top_x_values = missing_scan_df.groupby(level_x)['avg_missing_pct'].mean().nlargest(15).index.tolist()
                        top_y_values = missing_scan_df.groupby(level_y)['avg_missing_pct'].mean().nlargest(20).index.tolist()

                        # Filter to top values
                        filtered_missing_scan_df = missing_scan_df[
                            (missing_scan_df[level_x].isin(top_x_values)) &
                            (missing_scan_df[level_y].isin(top_y_values))
                        ]

                        # Create pivot table for heatmap
                        pivot_table = pd.pivot_table(
                            filtered_missing_scan_df,
                            values='avg_missing_pct',
                            index=level_y,
                            columns=level_x,
                            aggfunc='mean'
                        )

                        # Check if we have data in the pivot table
                        if not pivot_table.empty:
                            # Prepare additional data for hover information
                            hover_data = []
                            for y_id in pivot_table.index:
                                row_data = []
                                for x_id in pivot_table.columns:
                                    # Find the corresponding row in the original data
                                    data_row = filtered_missing_scan_df[
                                        (filtered_missing_scan_df[level_x] == x_id) &
                                        (filtered_missing_scan_df[level_y] == y_id)
                                    ]
                                    if not data_row.empty:
                                        # Get the detailed counts
                                        total_records = data_row['total_records'].iloc[0]
                                        missing_entry = data_row['missing_entry_pct'].iloc[0]
                                        missing_exit = data_row['missing_exit_pct'].iloc[0]
                                        missing_delivery = data_row['missing_delivery_pct'].iloc[0]
                                        row_data.append([
                                            total_records,
                                            missing_entry,
                                            missing_exit,
                                            missing_delivery
                                        ])
                                    else:
                                        row_data.append([0, 0, 0, 0])
                                hover_data.append(row_data)

                            # Create heatmap with enhanced hover information
                            fig_heatmap = go.Figure(data=go.Heatmap(
                                z=pivot_table.values,
                                x=pivot_table.columns,
                                y=pivot_table.index,
                                colorscale='Reds',
                                colorbar=dict(title='Avg Missing %'),
                                hovertemplate=(
                                    f'<b>{self._format_level_name(level_x)}</b>: %{{x}}<br>' +
                                    f'<b>{self._format_level_name(level_y)}</b>: %{{y}}<br>' +
                                    f'<b>Overall Missing</b>: %{{z:.1f}}%<br>' +
                                    f'<b>Missing Entry Scans</b>: %{{customdata[1]:.1f}}%<br>' +
                                    f'<b>Missing Exit Scans</b>: %{{customdata[2]:.1f}}%<br>' +
                                    f'<b>Missing Delivery Scans</b>: %{{customdata[3]:.1f}}%<br>' +
                                    f'<b>Total Records</b>: %{{customdata[0]}}<br>' +
                                    '<extra></extra>'
                                ),
                                customdata=hover_data
                            ))

                            # Update layout
                            fig_heatmap.update_layout(
                                title=f"Hierarchical Missing Scan Heatmap - Top {len(top_x_values)} {self._format_level_name(level_x)}s vs Top {len(top_y_values)} {self._format_level_name(level_y)}s{filter_title}",
                                xaxis_title=self._format_level_name(level_x),
                                yaxis_title=self._format_level_name(level_y),
                                template=self.plot_template,
                                height=600,  # Increase height for better readability
                                margin=dict(l=150, r=50, t=80, b=50)  # Increase left margin for y-axis labels
                            )

                            # Set consistent size
                            self.set_consistent_size(fig_heatmap)

                            # Add to figures list
                            figures.append(fig_heatmap)

                            # Add comparison with raw data if available
                            if has_comparison:
                                # Calculate missing scan percentages for raw data
                                raw_missing_scan_data = []

                                # Group by both levels
                                raw_grouped = filtered_df_raw.groupby([level_x, level_y])

                                for (x_id, y_id), group in raw_grouped:
                                    # Skip if either ID is None or NaN
                                    if pd.isna(x_id) or pd.isna(y_id) or x_id is None or y_id is None:
                                        continue

                                    # Calculate missing scan percentages
                                    total = len(group)
                                    missing_entry_pct = (group[~group['has_entry']].shape[0] / total) * 100 if total > 0 else 0
                                    missing_exit_pct = (group[~group['has_exit']].shape[0] / total) * 100 if total > 0 else 0
                                    missing_delivery_pct = (group[~group['has_delivery']].shape[0] / total) * 100 if total > 0 else 0

                                    # Calculate average missing percentage
                                    avg_missing_pct = (missing_entry_pct + missing_exit_pct + missing_delivery_pct) / 3

                                    # Store data
                                    raw_missing_scan_data.append({
                                        level_x: x_id,
                                        level_y: y_id,
                                        'avg_missing_pct': avg_missing_pct
                                    })

                                # Convert to DataFrame
                                if raw_missing_scan_data:
                                    raw_missing_scan_df = pd.DataFrame(raw_missing_scan_data)

                                    # Filter to the same top values as the cleaned data
                                    filtered_raw_missing_scan_df = raw_missing_scan_df[
                                        (raw_missing_scan_df[level_x].isin(top_x_values)) &
                                        (raw_missing_scan_df[level_y].isin(top_y_values))
                                    ]

                                    # Create pivot table for raw data heatmap
                                    raw_pivot_table = pd.pivot_table(
                                        filtered_raw_missing_scan_df,
                                        values='avg_missing_pct',
                                        index=level_y,
                                        columns=level_x,
                                        aggfunc='mean'
                                    )

                                    # Prepare additional data for hover information for raw data
                                    raw_hover_data = []
                                    for y_id in raw_pivot_table.index:
                                        row_data = []
                                        for x_id in raw_pivot_table.columns:
                                            # Find the corresponding row in the original data
                                            data_row = raw_missing_scan_df[
                                                (raw_missing_scan_df[level_x] == x_id) &
                                                (raw_missing_scan_df[level_y] == y_id)
                                            ]
                                            if not data_row.empty:
                                                # Get the detailed counts
                                                total_records = data_row['total_records'].iloc[0]
                                                missing_entry = data_row['missing_entry_pct'].iloc[0]
                                                missing_exit = data_row['missing_exit_pct'].iloc[0]
                                                missing_delivery = data_row['missing_delivery_pct'].iloc[0]
                                                row_data.append([
                                                    total_records,
                                                    missing_entry,
                                                    missing_exit,
                                                    missing_delivery
                                                ])
                                            else:
                                                row_data.append([0, 0, 0, 0])
                                        raw_hover_data.append(row_data)

                                    # Create heatmap for raw data with enhanced hover information
                                    fig_raw_heatmap = go.Figure(data=go.Heatmap(
                                        z=raw_pivot_table.values,
                                        x=raw_pivot_table.columns,
                                        y=raw_pivot_table.index,
                                        colorscale='Blues',
                                        colorbar=dict(title='Avg Missing % (Before)'),
                                        hovertemplate=(
                                            f'<b>{self._format_level_name(level_x)}</b>: %{{x}}<br>' +
                                            f'<b>{self._format_level_name(level_y)}</b>: %{{y}}<br>' +
                                            f'<b>Overall Missing (Before)</b>: %{{z:.1f}}%<br>' +
                                            f'<b>Missing Entry Scans</b>: %{{customdata[1]:.1f}}%<br>' +
                                            f'<b>Missing Exit Scans</b>: %{{customdata[2]:.1f}}%<br>' +
                                            f'<b>Missing Delivery Scans</b>: %{{customdata[3]:.1f}}%<br>' +
                                            f'<b>Total Records</b>: %{{customdata[0]}}<br>' +
                                            '<extra></extra>'
                                        ),
                                        customdata=raw_hover_data
                                    ))

                                    # Update layout
                                    fig_raw_heatmap.update_layout(
                                        title=f"Hierarchical Missing Scan Heatmap (Before Cleaning){filter_title}",
                                        xaxis_title=self._format_level_name(level_x),
                                        yaxis_title=self._format_level_name(level_y),
                                        template=self.plot_template,
                                        height=600,
                                        margin=dict(l=150, r=50, t=80, b=50)
                                    )

                                    # Set consistent size
                                    self.set_consistent_size(fig_raw_heatmap)

                                    # Add to figures list
                                    figures.append(fig_raw_heatmap)

                                    # Create difference heatmap
                                    # Ensure both pivot tables have the same indices and columns
                                    common_indices = set(pivot_table.index).intersection(set(raw_pivot_table.index))
                                    common_columns = set(pivot_table.columns).intersection(set(raw_pivot_table.columns))

                                    if common_indices and common_columns:
                                        # Filter to common indices and columns
                                        pivot_filtered = pivot_table.loc[list(common_indices), list(common_columns)]
                                        raw_pivot_filtered = raw_pivot_table.loc[list(common_indices), list(common_columns)]

                                        # Calculate difference (improvement)
                                        diff_values = raw_pivot_filtered.values - pivot_filtered.values

                                        # Prepare additional data for hover information for difference heatmap
                                        diff_hover_data = []
                                        for y_idx, y_id in enumerate(common_indices):
                                            row_data = []
                                            for x_idx, x_id in enumerate(common_columns):
                                                # Get the before and after values
                                                before_val = raw_pivot_table.loc[y_id, x_id] if y_id in raw_pivot_table.index and x_id in raw_pivot_table.columns else 0
                                                after_val = pivot_table.loc[y_id, x_id] if y_id in pivot_table.index and x_id in pivot_table.columns else 0

                                                # Find the corresponding rows in the original data
                                                raw_row = raw_missing_scan_df[
                                                    (raw_missing_scan_df[level_x] == x_id) &
                                                    (raw_missing_scan_df[level_y] == y_id)
                                                ]
                                                cleaned_row = filtered_missing_scan_df[
                                                    (filtered_missing_scan_df[level_x] == x_id) &
                                                    (filtered_missing_scan_df[level_y] == y_id)
                                                ]

                                                # Get the detailed counts
                                                if not raw_row.empty and not cleaned_row.empty:
                                                    # Before values
                                                    before_entry = raw_row['missing_entry_pct'].iloc[0]
                                                    before_exit = raw_row['missing_exit_pct'].iloc[0]
                                                    before_delivery = raw_row['missing_delivery_pct'].iloc[0]

                                                    # After values
                                                    after_entry = cleaned_row['missing_entry_pct'].iloc[0]
                                                    after_exit = cleaned_row['missing_exit_pct'].iloc[0]
                                                    after_delivery = cleaned_row['missing_delivery_pct'].iloc[0]

                                                    # Calculate improvements
                                                    entry_improvement = before_entry - after_entry
                                                    exit_improvement = before_exit - after_exit
                                                    delivery_improvement = before_delivery - after_delivery

                                                    row_data.append([
                                                        before_val,
                                                        after_val,
                                                        entry_improvement,
                                                        exit_improvement,
                                                        delivery_improvement
                                                    ])
                                                else:
                                                    row_data.append([0, 0, 0, 0, 0])
                                            diff_hover_data.append(row_data)

                                        # Create difference heatmap with enhanced hover information
                                        fig_diff_heatmap = go.Figure(data=go.Heatmap(
                                            z=diff_values,
                                            x=list(common_columns),
                                            y=list(common_indices),
                                            colorscale='RdBu',  # Red for negative (worse), Blue for positive (better)
                                            zmid=0,  # Center the color scale at 0
                                            colorbar=dict(title='Improvement %'),
                                            hovertemplate=(
                                                f'<b>{self._format_level_name(level_x)}</b>: %{{x}}<br>' +
                                                f'<b>{self._format_level_name(level_y)}</b>: %{{y}}<br>' +
                                                f'<b>Overall Improvement</b>: %{{z:.1f}}%<br>' +
                                                f'<b>Before Cleaning</b>: %{{customdata[0]:.1f}}%<br>' +
                                                f'<b>After Cleaning</b>: %{{customdata[1]:.1f}}%<br>' +
                                                f'<b>Entry Scan Improvement</b>: %{{customdata[2]:.1f}}%<br>' +
                                                f'<b>Exit Scan Improvement</b>: %{{customdata[3]:.1f}}%<br>' +
                                                f'<b>Delivery Scan Improvement</b>: %{{customdata[4]:.1f}}%<br>' +
                                                '<extra></extra>'
                                            ),
                                            customdata=diff_hover_data
                                        ))

                                        # Update layout
                                        fig_diff_heatmap.update_layout(
                                            title=f"Hierarchical Missing Scan Improvement{filter_title}",
                                            xaxis_title=self._format_level_name(level_x),
                                            yaxis_title=self._format_level_name(level_y),
                                            template=self.plot_template,
                                            height=600,
                                            margin=dict(l=150, r=50, t=80, b=50)
                                        )

                                        # Set consistent size
                                        self.set_consistent_size(fig_diff_heatmap)

                                        # Add to figures list
                                        figures.append(fig_diff_heatmap)
            except Exception as e:
                logger.error(f"Error creating hierarchical missing scan heatmap: {str(e)}", exc_info=True)

            # Create Missing Scan Patterns by Date and Entity
            if 'date' in filtered_df.columns and len(hierarchical_levels) > 0:
                try:
                    # Choose the most appropriate hierarchical level
                    entity_level = hierarchical_levels[0]  # Start with the highest level

                    # If we have too many entities at the highest level, try the next level
                    unique_entities = filtered_df[entity_level].nunique()
                    if unique_entities > 20 and len(hierarchical_levels) > 1:
                        entity_level = hierarchical_levels[1]

                    # Group by date and entity
                    grouped = filtered_df.groupby([filtered_df['date'].dt.date, entity_level])

                    # Calculate missing scan percentages for each date and entity
                    pattern_data = []

                    for (date, entity_id), group in grouped:
                        # Skip if entity_id is None or NaN
                        if pd.isna(entity_id) or entity_id is None:
                            continue

                        # Calculate missing scan percentages
                        total = len(group)
                        missing_entry_pct = (group[~group['has_entry']].shape[0] / total) * 100 if total > 0 else 0
                        missing_exit_pct = (group[~group['has_exit']].shape[0] / total) * 100 if total > 0 else 0
                        missing_delivery_pct = (group[~group['has_delivery']].shape[0] / total) * 100 if total > 0 else 0

                        # Calculate average missing percentage
                        avg_missing_pct = (missing_entry_pct + missing_exit_pct + missing_delivery_pct) / 3

                        # Store data
                        pattern_data.append({
                            'date': date,
                            'entity_id': entity_id,
                            'missing_entry_pct': missing_entry_pct,
                            'missing_exit_pct': missing_exit_pct,
                            'missing_delivery_pct': missing_delivery_pct,
                            'avg_missing_pct': avg_missing_pct,
                            'total_records': total
                        })

                    # Convert to DataFrame
                    if pattern_data:
                        pattern_df = pd.DataFrame(pattern_data)

                        # Create pivot table for heatmap
                        pivot_table = pd.pivot_table(
                            pattern_df,
                            values='avg_missing_pct',
                            index='entity_id',
                            columns='date',
                            aggfunc='mean'
                        )

                        # Check if we have data in the pivot table
                        if not pivot_table.empty:
                            # Prepare additional data for hover information
                            pattern_hover_data = []
                            for y_id in pivot_table.index:
                                row_data = []
                                for x_id in pivot_table.columns:
                                    # Find the corresponding rows in the original data
                                    data_rows = missing_scan_df[
                                        (missing_scan_df['date_str'] == x_id) &
                                        (missing_scan_df[entity_level] == y_id)
                                    ]

                                    if not data_rows.empty:
                                        # Get the detailed counts
                                        total_records = data_rows['total_records'].sum()
                                        missing_entry = data_rows['missing_entry_count'].sum()
                                        missing_exit = data_rows['missing_exit_count'].sum()
                                        missing_delivery = data_rows['missing_delivery_count'].sum()

                                        # Calculate percentages
                                        missing_entry_pct = (missing_entry / total_records * 100) if total_records > 0 else 0
                                        missing_exit_pct = (missing_exit / total_records * 100) if total_records > 0 else 0
                                        missing_delivery_pct = (missing_delivery / total_records * 100) if total_records > 0 else 0

                                        row_data.append([
                                            total_records,
                                            missing_entry,
                                            missing_exit,
                                            missing_delivery,
                                            missing_entry_pct,
                                            missing_exit_pct,
                                            missing_delivery_pct
                                        ])
                                    else:
                                        row_data.append([0, 0, 0, 0, 0, 0, 0])
                                pattern_hover_data.append(row_data)

                            # Create heatmap with enhanced hover information
                            fig_pattern = go.Figure(data=go.Heatmap(
                                z=pivot_table.values,
                                x=pivot_table.columns,
                                y=pivot_table.index,
                                colorscale='Reds',
                                colorbar=dict(title='Avg Missing %'),
                                hovertemplate=(
                                    f'<b>Date</b>: %{{x}}<br>' +
                                    f'<b>{self._format_level_name(entity_level)}</b>: %{{y}}<br>' +
                                    f'<b>Overall Missing</b>: %{{z:.1f}}%<br>' +
                                    f'<b>Total Records</b>: %{{customdata[0]}}<br>' +
                                    f'<b>Missing Entry Scans</b>: %{{customdata[1]}} (%{{customdata[4]:.1f}}%)<br>' +
                                    f'<b>Missing Exit Scans</b>: %{{customdata[2]}} (%{{customdata[5]:.1f}}%)<br>' +
                                    f'<b>Missing Delivery Scans</b>: %{{customdata[3]}} (%{{customdata[6]:.1f}}%)<br>' +
                                    '<extra></extra>'
                                ),
                                customdata=pattern_hover_data
                            ))

                            # Update layout
                            fig_pattern.update_layout(
                                title=f"Missing Scan Patterns by Date and {self._format_level_name(entity_level)}{filter_title}",
                                xaxis_title="Date",
                                yaxis_title=self._format_level_name(entity_level),
                                template=self.plot_template
                            )

                            # Set consistent size
                            self.set_consistent_size(fig_pattern)

                            # Add to figures list
                            figures.append(fig_pattern)

                            # If we have comparison data, create a similar visualization for raw data
                            if has_comparison and 'date' in filtered_df_raw.columns:
                                # Group by date and entity for raw data
                                grouped_raw = filtered_df_raw.groupby([filtered_df_raw['date'].dt.date, entity_level])

                                # Calculate missing scan percentages for each date and entity in raw data
                                pattern_data_raw = []

                                for (date, entity_id), group in grouped_raw:
                                    # Skip if entity_id is None or NaN
                                    if pd.isna(entity_id) or entity_id is None:
                                        continue

                                    # Calculate missing scan percentages
                                    total = len(group)
                                    missing_entry_pct = (group[~group['has_entry']].shape[0] / total) * 100 if total > 0 else 0
                                    missing_exit_pct = (group[~group['has_exit']].shape[0] / total) * 100 if total > 0 else 0
                                    missing_delivery_pct = (group[~group['has_delivery']].shape[0] / total) * 100 if total > 0 else 0

                                    # Calculate average missing percentage
                                    avg_missing_pct = (missing_entry_pct + missing_exit_pct + missing_delivery_pct) / 3

                                    # Store data
                                    pattern_data_raw.append({
                                        'date': date,
                                        'entity_id': entity_id,
                                        'missing_entry_pct': missing_entry_pct,
                                        'missing_exit_pct': missing_exit_pct,
                                        'missing_delivery_pct': missing_delivery_pct,
                                        'avg_missing_pct': avg_missing_pct,
                                        'total_records': total
                                    })

                                # Convert to DataFrame
                                if pattern_data_raw:
                                    pattern_df_raw = pd.DataFrame(pattern_data_raw)

                                    # Create pivot table for heatmap
                                    pivot_table_raw = pd.pivot_table(
                                        pattern_df_raw,
                                        values='avg_missing_pct',
                                        index='entity_id',
                                        columns='date',
                                        aggfunc='mean'
                                    )

                                    # Check if we have data in the pivot table
                                    if not pivot_table_raw.empty:
                                        # Prepare additional data for hover information for raw data
                                        pattern_raw_hover_data = []
                                        for y_id in pivot_table_raw.index:
                                            row_data = []
                                            for x_id in pivot_table_raw.columns:
                                                # Find the corresponding rows in the original data
                                                data_rows = raw_missing_scan_df[
                                                    (raw_missing_scan_df['date_str'] == x_id) &
                                                    (raw_missing_scan_df[entity_level] == y_id)
                                                ]

                                                if not data_rows.empty:
                                                    # Get the detailed counts
                                                    total_records = data_rows['total_records'].sum()
                                                    missing_entry = data_rows['missing_entry_count'].sum()
                                                    missing_exit = data_rows['missing_exit_count'].sum()
                                                    missing_delivery = data_rows['missing_delivery_count'].sum()

                                                    # Calculate percentages
                                                    missing_entry_pct = (missing_entry / total_records * 100) if total_records > 0 else 0
                                                    missing_exit_pct = (missing_exit / total_records * 100) if total_records > 0 else 0
                                                    missing_delivery_pct = (missing_delivery / total_records * 100) if total_records > 0 else 0

                                                    row_data.append([
                                                        total_records,
                                                        missing_entry,
                                                        missing_exit,
                                                        missing_delivery,
                                                        missing_entry_pct,
                                                        missing_exit_pct,
                                                        missing_delivery_pct
                                                    ])
                                                else:
                                                    row_data.append([0, 0, 0, 0, 0, 0, 0])
                                            pattern_raw_hover_data.append(row_data)

                                        # Create heatmap for raw data with enhanced hover information
                                        fig_pattern_raw = go.Figure(data=go.Heatmap(
                                            z=pivot_table_raw.values,
                                            x=pivot_table_raw.columns,
                                            y=pivot_table_raw.index,
                                            colorscale='Blues',
                                            colorbar=dict(title='Avg Missing % (Before)'),
                                            hovertemplate=(
                                                f'<b>Date</b>: %{{x}}<br>' +
                                                f'<b>{self._format_level_name(entity_level)}</b>: %{{y}}<br>' +
                                                f'<b>Overall Missing (Before)</b>: %{{z:.1f}}%<br>' +
                                                f'<b>Total Records</b>: %{{customdata[0]}}<br>' +
                                                f'<b>Missing Entry Scans</b>: %{{customdata[1]}} (%{{customdata[4]:.1f}}%)<br>' +
                                                f'<b>Missing Exit Scans</b>: %{{customdata[2]}} (%{{customdata[5]:.1f}}%)<br>' +
                                                f'<b>Missing Delivery Scans</b>: %{{customdata[3]}} (%{{customdata[6]:.1f}}%)<br>' +
                                                '<extra></extra>'
                                            ),
                                            customdata=pattern_raw_hover_data
                                        ))

                                        # Update layout
                                        fig_pattern_raw.update_layout(
                                            title=f"Missing Scan Patterns by Date and {self._format_level_name(entity_level)} (Before Cleaning){filter_title}",
                                            xaxis_title="Date",
                                            yaxis_title=self._format_level_name(entity_level),
                                            template=self.plot_template
                                        )

                                        # Set consistent size
                                        self.set_consistent_size(fig_pattern_raw)

                                        # Add to figures list
                                        figures.append(fig_pattern_raw)

                                        # Create a difference heatmap to show improvement
                                        # First, ensure both pivot tables have the same indices and columns
                                        common_indices = set(pivot_table.index).intersection(set(pivot_table_raw.index))
                                        common_columns = set(pivot_table.columns).intersection(set(pivot_table_raw.columns))

                                        if common_indices and common_columns:
                                            # Filter to common indices and columns
                                            pivot_table_filtered = pivot_table.loc[list(common_indices), list(common_columns)]
                                            pivot_table_raw_filtered = pivot_table_raw.loc[list(common_indices), list(common_columns)]

                                            # Calculate difference (improvement)
                                            diff_values = pivot_table_raw_filtered.values - pivot_table_filtered.values

                                            # Prepare additional data for hover information for difference heatmap
                                            pattern_diff_hover_data = []
                                            for y_idx, y_id in enumerate(common_indices):
                                                row_data = []
                                                for x_idx, x_id in enumerate(common_columns):
                                                    # Get the before and after values
                                                    before_val = pivot_table_raw.loc[y_id, x_id] if y_id in pivot_table_raw.index and x_id in pivot_table_raw.columns else 0
                                                    after_val = pivot_table.loc[y_id, x_id] if y_id in pivot_table.index and x_id in pivot_table.columns else 0

                                                    # Find the corresponding rows in the original data
                                                    raw_rows = raw_missing_scan_df[
                                                        (raw_missing_scan_df['date_str'] == x_id) &
                                                        (raw_missing_scan_df[entity_level] == y_id)
                                                    ]
                                                    cleaned_rows = missing_scan_df[
                                                        (missing_scan_df['date_str'] == x_id) &
                                                        (missing_scan_df[entity_level] == y_id)
                                                    ]

                                                    if not raw_rows.empty and not cleaned_rows.empty:
                                                        # Get the detailed counts for before
                                                        before_total = raw_rows['total_records'].sum()
                                                        before_missing_entry = raw_rows['missing_entry_count'].sum()
                                                        before_missing_exit = raw_rows['missing_exit_count'].sum()
                                                        before_missing_delivery = raw_rows['missing_delivery_count'].sum()

                                                        # Get the detailed counts for after
                                                        after_total = cleaned_rows['total_records'].sum()
                                                        after_missing_entry = cleaned_rows['missing_entry_count'].sum()
                                                        after_missing_exit = cleaned_rows['missing_exit_count'].sum()
                                                        after_missing_delivery = cleaned_rows['missing_delivery_count'].sum()

                                                        # Calculate percentages for before
                                                        before_entry_pct = (before_missing_entry / before_total * 100) if before_total > 0 else 0
                                                        before_exit_pct = (before_missing_exit / before_total * 100) if before_total > 0 else 0
                                                        before_delivery_pct = (before_missing_delivery / before_total * 100) if before_total > 0 else 0

                                                        # Calculate percentages for after
                                                        after_entry_pct = (after_missing_entry / after_total * 100) if after_total > 0 else 0
                                                        after_exit_pct = (after_missing_exit / after_total * 100) if after_total > 0 else 0
                                                        after_delivery_pct = (after_missing_delivery / after_total * 100) if after_total > 0 else 0

                                                        # Calculate improvements
                                                        entry_improvement = before_entry_pct - after_entry_pct
                                                        exit_improvement = before_exit_pct - after_exit_pct
                                                        delivery_improvement = before_delivery_pct - after_delivery_pct

                                                        row_data.append([
                                                            before_val,
                                                            after_val,
                                                            before_entry_pct,
                                                            after_entry_pct,
                                                            before_exit_pct,
                                                            after_exit_pct,
                                                            before_delivery_pct,
                                                            after_delivery_pct,
                                                            entry_improvement,
                                                            exit_improvement,
                                                            delivery_improvement
                                                        ])
                                                    else:
                                                        row_data.append([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
                                                pattern_diff_hover_data.append(row_data)

                                            # Create difference heatmap with enhanced hover information
                                            fig_pattern_diff = go.Figure(data=go.Heatmap(
                                                z=diff_values,
                                                x=list(common_columns),
                                                y=list(common_indices),
                                                colorscale='RdBu',  # Red for negative (worse), Blue for positive (better)
                                                zmid=0,  # Center the color scale at 0
                                                colorbar=dict(title='Improvement %'),
                                                hovertemplate=(
                                                    f'<b>Date</b>: %{{x}}<br>' +
                                                    f'<b>{self._format_level_name(entity_level)}</b>: %{{y}}<br>' +
                                                    f'<b>Overall Improvement</b>: %{{z:.1f}}%<br>' +
                                                    f'<b>Before Cleaning</b>: %{{customdata[0]:.1f}}%<br>' +
                                                    f'<b>After Cleaning</b>: %{{customdata[1]:.1f}}%<br>' +
                                                    f'<b>Entry Scan</b>: Before %{{customdata[2]:.1f}}% → After %{{customdata[3]:.1f}}% (Improvement: %{{customdata[8]:.1f}}%)<br>' +
                                                    f'<b>Exit Scan</b>: Before %{{customdata[4]:.1f}}% → After %{{customdata[5]:.1f}}% (Improvement: %{{customdata[9]:.1f}}%)<br>' +
                                                    f'<b>Delivery Scan</b>: Before %{{customdata[6]:.1f}}% → After %{{customdata[7]:.1f}}% (Improvement: %{{customdata[10]:.1f}}%)<br>' +
                                                    '<extra></extra>'
                                                ),
                                                customdata=pattern_diff_hover_data
                                            ))

                                            # Update layout
                                            fig_pattern_diff.update_layout(
                                                title=f"Missing Scan Improvement by Date and {self._format_level_name(entity_level)}{filter_title}",
                                                xaxis_title="Date",
                                                yaxis_title=self._format_level_name(entity_level),
                                                template=self.plot_template
                                            )

                                            # Set consistent size
                                            self.set_consistent_size(fig_pattern_diff)

                                            # Add to figures list
                                            figures.append(fig_pattern_diff)
                except Exception as e:
                    logger.error(f"Error creating missing scan patterns visualization: {str(e)}", exc_info=True)

            # Return the figures, metrics, tabular data, filled values data, and comparison flag
            result = {
                'figures': figures,
                'metrics': metrics,
                'tabular_data': tabular_data.to_dict('records') if tabular_data is not None and not tabular_data.empty else None,
                'has_comparison': has_comparison
            }

            # Add any additional data from return_data
            if return_data:
                result.update(return_data)

            return result

        except Exception as e:
            logger.error(f"Error analyzing missing scans: {str(e)}", exc_info=True)
            # Return a simple error figure
            error_fig = go.Figure().add_annotation(
                text=f"Error analyzing missing scans: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=16, color='red')
            )
            return {
                'figures': [error_fig],
                'metrics': {},
                'has_comparison': False
            }
