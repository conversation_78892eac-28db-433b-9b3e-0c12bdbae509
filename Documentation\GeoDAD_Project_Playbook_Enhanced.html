<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoDAD Project Playbook</title>
    <link rel="icon" href="images/favicon.svg" type="image/svg+xml">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Color palette */
            --primary-color: #3498db;
            --primary-dark: #2980b9;
            --secondary-color: #2ecc71;
            --secondary-dark: #27ae60;
            --accent-color: #e74c3c;
            --accent-dark: #c0392b;
            --text-color: #333333;
            --text-light: #666666;
            --background-color: #ffffff;
            --background-light: #f8f9fa;
            --background-dark: #e9ecef;
            --border-color: #dee2e6;

            /* Component colors */
            --data-processor-color: #e74c3c;
            --visualization-color: #2ecc71;
            --map-handler-color: #f39c12;
            --report-color: #9b59b6;
            --analyzer-color: #1abc9c;

            /* Typography */
            --heading-font: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            --body-font: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            --code-font: 'Roboto Mono', Consolas, Monaco, 'Andale Mono', monospace;

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-xxl: 3rem;

            /* Border radius */
            --border-radius-sm: 0.25rem;
            --border-radius-md: 0.5rem;
            --border-radius-lg: 0.75rem;

            /* Box shadow */
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
            --shadow-lg: 0 10px 20px rgba(0,0,0,0.1), 0 3px 6px rgba(0,0,0,0.05);

            /* Transitions */
            --transition-fast: 150ms ease;
            --transition-normal: 300ms ease;
            --transition-slow: 500ms ease;
        }

        /* Base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
            color: var(--text-color);
            background-color: var(--background-color);
        }

        body {
            font-family: var(--body-font);
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }

        /* Layout */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
        }

        .page-wrapper {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background-color: var(--background-light);
            border-right: 1px solid var(--border-color);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            padding: var(--spacing-lg);
            transition: transform var(--transition-normal);
            z-index: 100;
        }

        .content {
            flex: 1;
            margin-left: 280px;
            padding: var(--spacing-xl);
            max-width: calc(100% - 280px);
            width: 100%;
            box-sizing: border-box;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        section {
            width: 100%;
            box-sizing: border-box;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--heading-font);
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: var(--spacing-md);
            color: var(--text-color);
        }

        h1 {
            font-size: 2.5rem;
            margin-top: var(--spacing-xl);
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: var(--spacing-sm);
        }

        h2 {
            font-size: 2rem;
            margin-top: var(--spacing-xxl);
            padding-bottom: var(--spacing-xs);
            border-bottom: 1px solid var(--border-color);
        }

        h3 {
            font-size: 1.5rem;
            margin-top: var(--spacing-xl);
            color: var(--primary-dark);
        }

        h4 {
            font-size: 1.25rem;
            margin-top: var(--spacing-lg);
        }

        p {
            margin-bottom: var(--spacing-md);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Lists */
        ul, ol {
            margin-bottom: var(--spacing-md);
            padding-left: var(--spacing-xl);
        }

        li {
            margin-bottom: var(--spacing-xs);
        }

        /* Code blocks */
        code {
            font-family: var(--code-font);
            background-color: var(--background-light);
            padding: 0.2em 0.4em;
            border-radius: var(--border-radius-sm);
            font-size: 0.9em;
            color: var(--accent-color);
        }

        pre {
            font-family: var(--code-font);
            background-color: var(--background-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            overflow-x: auto;
            margin-bottom: var(--spacing-md);
            border: 1px solid var(--border-color);
        }

        pre code {
            background-color: transparent;
            padding: 0;
            color: inherit;
        }

        /* ASCII diagrams */
        .ascii-diagram {
            font-family: var(--code-font);
            line-height: 1.2;
            white-space: pre;
            background-color: var(--background-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            overflow-x: auto;
            margin: var(--spacing-md) 0;
            border: 1px solid var(--border-color);
        }

        /* Tables */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: var(--spacing-md) 0;
            overflow-x: auto;
            display: block;
        }

        th, td {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            text-align: left;
        }

        th {
            background-color: var(--background-light);
            font-weight: 600;
        }

        tr:nth-child(even) {
            background-color: var(--background-light);
        }

        /* Navigation */
        .nav-logo {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }

        .nav-logo img {
            width: 40px;
            height: 40px;
            margin-right: var(--spacing-sm);
        }

        .nav-logo h2 {
            font-size: 1.5rem;
            margin: 0;
            border: none;
        }

        .nav-section {
            margin-bottom: var(--spacing-md);
        }

        .nav-section-title {
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .nav-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: var(--spacing-xs);
        }

        .nav-link {
            display: block;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            transition: background-color var(--transition-fast);
            color: var(--text-color);
            text-decoration: none;
        }

        .nav-link:hover {
            background-color: var(--background-dark);
            text-decoration: none;
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-subsection {
            list-style: none;
            padding-left: var(--spacing-md);
            margin: var(--spacing-xs) 0;
        }

        /* Components */
        .card {
            background-color: var(--background-color);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            border: 1px solid var(--border-color);
            width: 100%;
            box-sizing: border-box;
        }

        .info-box {
            background-color: rgba(52, 152, 219, 0.1);
            border-left: 4px solid var(--primary-color);
            padding: var(--spacing-md);
            margin: var(--spacing-md) 0;
            border-radius: var(--border-radius-sm);
        }

        .warning-box {
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 4px solid var(--accent-color);
            padding: var(--spacing-md);
            margin: var(--spacing-md) 0;
            border-radius: var(--border-radius-sm);
        }

        .tip-box {
            background-color: rgba(46, 204, 113, 0.1);
            border-left: 4px solid var(--secondary-color);
            padding: var(--spacing-md);
            margin: var(--spacing-md) 0;
            border-radius: var(--border-radius-sm);
        }

        /* Diagrams */
        .diagram-container {
            margin: var(--spacing-lg) 0;
            text-align: center;
        }

        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .diagram-caption {
            margin-top: var(--spacing-sm);
            font-style: italic;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Tabs */
        .tabs {
            margin: var(--spacing-lg) 0;
        }

        .tab-nav {
            display: flex;
            list-style: none;
            padding: 0;
            margin: 0;
            border-bottom: 1px solid var(--border-color);
        }

        .tab-nav-item {
            padding: var(--spacing-sm) var(--spacing-md);
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
            margin-right: var(--spacing-xs);
            background-color: var(--background-light);
        }

        .tab-nav-item.active {
            background-color: var(--background-color);
            border-color: var(--border-color);
            border-bottom-color: var(--background-color);
            font-weight: 500;
        }

        .tab-content {
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* Accordion */
        .accordion {
            margin: var(--spacing-md) 0;
        }

        .accordion-item {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            margin-bottom: var(--spacing-xs);
            overflow: hidden;
        }

        .accordion-header {
            padding: var(--spacing-md);
            background-color: var(--background-light);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 500;
        }

        .accordion-header:hover {
            background-color: var(--background-dark);
        }

        .accordion-content {
            padding: var(--spacing-md);
            display: none;
            border-top: 1px solid var(--border-color);
        }

        .accordion-item.active .accordion-content {
            display: block;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            font-family: var(--body-font);
            font-size: 1rem;
            transition: background-color var(--transition-fast);
            text-decoration: none;
        }

        .btn:hover {
            background-color: var(--primary-dark);
            text-decoration: none;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: var(--secondary-dark);
        }

        .btn-accent {
            background-color: var(--accent-color);
        }

        .btn-accent:hover {
            background-color: var(--accent-dark);
        }

        /* Badges */
        .badge {
            display: inline-block;
            padding: 0.25em 0.6em;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 10px;
            color: white;
            background-color: var(--primary-color);
            margin-left: var(--spacing-xs);
        }

        .badge-secondary {
            background-color: var(--secondary-color);
        }

        .badge-accent {
            background-color: var(--accent-color);
        }

        /* Module colors */
        .module-data-processor {
            color: var(--data-processor-color);
        }

        .module-visualization {
            color: var(--visualization-color);
        }

        .module-map-handler {
            color: var(--map-handler-color);
        }

        .module-report {
            color: var(--report-color);
        }

        .module-analyzer {
            color: var(--analyzer-color);
        }

        /* Responsive styles */
        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }

            .content {
                margin-left: 240px;
                max-width: calc(100% - 240px);
                width: 100%;
                box-sizing: border-box;
                padding: var(--spacing-lg);
            }
        }

        @media (max-width: 768px) {
            .page-wrapper {
                flex-direction: column;
            }

            .sidebar {
                width: 280px;
                height: 100vh;
                position: fixed;
                left: -280px;
                top: 0;
                z-index: 1000;
                box-shadow: var(--shadow-lg);
                transition: left var(--transition-normal);
                overflow-y: auto;
            }

            .sidebar.expanded {
                left: 0;
            }

            .content {
                margin-left: 0;
                max-width: 100%;
                width: 100%;
                padding: var(--spacing-md);
                box-sizing: border-box;
                min-height: 100vh;
            }

            .mobile-nav-toggle {
                display: flex;
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 1001;
                background-color: var(--primary-color);
                color: white;
                border-radius: 50%;
                width: 3rem;
                height: 3rem;
                justify-content: center;
                align-items: center;
                box-shadow: var(--shadow-md);
                cursor: pointer;
            }

            /* Improve diagram responsiveness */
            .diagram-image {
                max-width: 100%;
                height: auto;
            }

            /* Improve ASCII diagram responsiveness */
            .ascii-diagram {
                font-size: 0.8rem;
                overflow-x: auto;
                white-space: pre;
            }

            /* Improve tab navigation responsiveness */
            .tab-nav {
                flex-wrap: wrap;
            }

            .tab-nav-item {
                margin-bottom: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            html {
                font-size: 14px;
            }

            .content {
                padding: var(--spacing-sm);
            }

            h1 {
                font-size: 1.8rem;
                margin-top: 3rem;
            }

            h2 {
                font-size: 1.5rem;
            }

            h3 {
                font-size: 1.3rem;
            }

            .card {
                padding: var(--spacing-sm);
            }

            .accordion-header {
                padding: var(--spacing-sm);
            }

            .accordion-content {
                padding: var(--spacing-sm);
            }

            .tab-nav-item {
                padding: var(--spacing-xs) var(--spacing-sm);
                font-size: 0.9rem;
            }

            .diagram-container {
                margin: var(--spacing-sm) 0;
            }

            .diagram-caption {
                font-size: 0.8rem;
            }

            .code-block {
                padding: var(--spacing-sm);
                font-size: 0.8rem;
            }

            .info-box, .warning-box, .tip-box {
                padding: var(--spacing-sm);
                margin: var(--spacing-sm) 0;
            }
        }

        /* Print styles */
        @media print {
            .sidebar {
                display: none;
            }

            .content {
                margin-left: 0;
                max-width: 100%;
            }

            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid;
                page-break-inside: avoid;
            }

            img, table, figure {
                page-break-inside: avoid;
            }

            a {
                color: var(--text-color);
                text-decoration: none;
            }
        }

        /* Utility classes */
        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .text-muted {
            color: var(--text-light);
        }

        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
            width: 100%;
            box-sizing: border-box;
        }

        .row > .card {
            flex: 1 0 calc(33.333% - 30px);
            margin: 0 15px 30px;
            min-width: 250px;
        }

        @media (max-width: 992px) {
            .row > .card {
                flex: 1 0 calc(50% - 30px);
            }
        }

        @media (max-width: 576px) {
            .row > .card {
                flex: 1 0 calc(100% - 30px);
            }
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .mb-1 {
            margin-bottom: var(--spacing-xs);
        }

        .mb-2 {
            margin-bottom: var(--spacing-sm);
        }

        .mb-3 {
            margin-bottom: var(--spacing-md);
        }

        .mb-4 {
            margin-bottom: var(--spacing-lg);
        }

        .mb-5 {
            margin-bottom: var(--spacing-xl);
        }

        .mt-0 {
            margin-top: 0;
        }

        .mt-1 {
            margin-top: var(--spacing-xs);
        }

        .mt-2 {
            margin-top: var(--spacing-sm);
        }

        .mt-3 {
            margin-top: var(--spacing-md);
        }

        .mt-4 {
            margin-top: var(--spacing-lg);
        }

        .mt-5 {
            margin-top: var(--spacing-xl);
        }

        /* Enterprise Header Styles */
        .enterprise-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            margin: -2rem -2rem 2rem -2rem;
            padding: 2.5rem 2rem;
            color: white;
            border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
            box-shadow: var(--shadow-md);
            position: relative;
        }

        .enterprise-header-content {
            display: flex;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .enterprise-logo {
            font-size: 3.5rem;
            margin-right: 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            width: 6rem;
            height: 6rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 1rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .enterprise-title {
            flex: 1;
        }

        .enterprise-title h1 {
            color: white;
            margin: 0;
            padding: 0;
            border: none;
            font-size: 2.8rem;
            line-height: 1.2;
            font-weight: 700;
            letter-spacing: -0.02em;
        }

        .enterprise-subtitle {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-top: 0.5rem;
            font-weight: 300;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.4rem 1rem;
            border-radius: 2rem;
            font-size: 0.9rem;
            font-weight: 500;
            margin-top: 1rem;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        /* Responsive enterprise header */
        @media (max-width: 768px) {
            .enterprise-header {
                padding: 2rem 1.5rem;
                margin: -1rem -1rem 1.5rem -1rem;
            }

            .enterprise-header-content {
                flex-direction: column;
                text-align: center;
            }

            .enterprise-logo {
                margin-right: 0;
                margin-bottom: 1rem;
                font-size: 3rem;
                width: 5rem;
                height: 5rem;
            }

            .enterprise-title h1 {
                font-size: 2.2rem;
            }

            .enterprise-subtitle {
                font-size: 1.2rem;
            }
        }


    </style>
</head>
<body>
    <div class="page-wrapper">
        <aside class="sidebar">
            <div class="nav-logo">
                <img src="images/favicon.svg" alt="GeoDAD Logo">
                <h2>GeoDAD</h2>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Getting Started</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#introduction" class="nav-link">Introduction</a></li>
                    <li class="nav-item"><a href="#installation" class="nav-link">Installation and Setup</a></li>
                    <li class="nav-item"><a href="#deployment" class="nav-link">Deployment Guide</a></li>
                    <li class="nav-item"><a href="#how-to-use" class="nav-link">How to Use This Playbook</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">System Overview</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#system-architecture" class="nav-link">System Architecture</a></li>
                    <li class="nav-item"><a href="#data-flow" class="nav-link">Data Flow</a></li>
                    <li class="nav-item"><a href="#user-interaction" class="nav-link">User Interaction</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Core Modules</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#app-controller" class="nav-link">App Controller</a></li>
                    <li class="nav-item"><a href="#data-processor" class="nav-link">Data Processor</a></li>
                    <li class="nav-item"><a href="#visualization" class="nav-link">Visualization</a></li>
                    <li class="nav-item"><a href="#map-handler" class="nav-link">Map Handler</a></li>
                    <li class="nav-item"><a href="#report-generator" class="nav-link">Report Generator</a></li>
                    <li class="nav-item"><a href="#unit-analyzer" class="nav-link">Unit Analyzer</a></li>
                    <li class="nav-item"><a href="#cleaning-report" class="nav-link">Cleaning Report</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Data Processing</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#data-upload" class="nav-link">Data Upload</a></li>
                    <li class="nav-item"><a href="#data-validation" class="nav-link">Data Validation</a></li>
                    <li class="nav-item"><a href="#data-cleaning" class="nav-link">Data Cleaning</a></li>
                    <li class="nav-item"><a href="#data-analysis" class="nav-link">Data Analysis</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Visualizations</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#hierarchical-treemap" class="nav-link">Hierarchical Treemap</a></li>
                    <li class="nav-item"><a href="#interactive-maps" class="nav-link">Interactive Maps</a></li>
                    <li class="nav-item"><a href="#statistical-charts" class="nav-link">Statistical Charts</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Advanced Features</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#spatial-analysis" class="nav-link">Spatial Analysis</a></li>
                    <li class="nav-item"><a href="#report-generation" class="nav-link">Report Generation</a></li>
                    <li class="nav-item"><a href="#custom-filtering" class="nav-link">Custom Filtering</a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Appendices</div>
                <ul class="nav-items">
                    <li class="nav-item"><a href="#common-faqs" class="nav-link">Common FAQs</a></li>
                    <li class="nav-item"><a href="#glossary" class="nav-link">Glossary</a></li>
                    <li class="nav-item"><a href="#conclusion" class="nav-link">Conclusion</a></li>
                </ul>
            </div>
        </aside>

        <main class="content">
            <section id="introduction">
                <div class="enterprise-header">
                    <div class="enterprise-header-content">
                        <div class="enterprise-logo">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="enterprise-title">
                            <h1>GeoDAD Project Playbook</h1>
                            <div class="enterprise-subtitle">Geographic Data Analysis Dashboard</div>
                            <div class="version-badge">Version 1.0 | April 24, 2025</div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <h3 class="mb-3"><i class="fas fa-info-circle"></i> About This Playbook</h3>
                    <p>Welcome to the GeoDAD Project Playbook! This comprehensive guide explains every aspect of the Geographic Data Analysis Dashboard project in a clear, accessible way. Whether you're a developer, business stakeholder, or just curious about the project, this playbook will help you understand how GeoDAD works.</p>

                    <div class="info-box">
                        <h4 class="mb-2"><i class="fas fa-lightbulb"></i> What is GeoDAD?</h4>
                        <p class="mb-0">The <strong>Geographic Data Analysis Dashboard (GeoDAD)</strong> is a web application designed to help analyze geographic data patterns in delivery systems. It provides tools for data validation, cleaning, visualization, and spatial analysis with a focus on hierarchical geographic data (complexes, buildings, units).</p>
                    </div>
                </div>

                <h2>Key Features</h2>
                <div class="row">
                    <div class="card mb-3">
                        <h4><i class="fas fa-database"></i> Data Processing</h4>
                        <p>Upload, validate, and clean geographic data with ease. The system automatically handles missing values, validates required fields, and prepares your data for analysis.</p>
                    </div>

                    <div class="card mb-3">
                        <h4><i class="fas fa-chart-bar"></i> Interactive Visualization</h4>
                        <p>Generate statistical charts, maps, and hierarchical visualizations to explore patterns in your data. All visualizations are interactive and can be filtered to focus on specific areas.</p>
                    </div>

                    <div class="card mb-3">
                        <h4><i class="fas fa-map"></i> Spatial Analysis</h4>
                        <p>Analyze unit-level geographic patterns, calculate centroids, and identify clusters. The system provides tools for understanding spatial relationships in your data.</p>
                    </div>

                    <div class="card mb-3">
                        <h4><i class="fas fa-file-pdf"></i> Report Generation</h4>
                        <p>Create comprehensive PDF reports with detailed analysis of your data. Reports include summary statistics, data quality metrics, and visualization placeholders.</p>
                    </div>

                    <div class="card mb-3">
                        <h4><i class="fas fa-sitemap"></i> Hierarchical Data Exploration</h4>
                        <p>Navigate through complex → building → unit hierarchy with ease. The system provides tools for exploring relationships between different levels of your data.</p>
                    </div>

                    <div class="card mb-3">
                        <h4><i class="fas fa-clock"></i> Time-Series Analysis</h4>
                        <p>Visualize temporal patterns in geographic data. The system provides tools for understanding how your data changes over time.</p>
                    </div>
                </div>

                <h2>Data Structure</h2>
                <p>The GeoDAD system works with hierarchical geographic data that follows this structure:</p>

                <div class="diagram-container">
                    <pre class="ascii-diagram">
┌─────────────────┐
│    Complex      │  (Highest level, optional)
│                 │
└────────┬────────┘
         │
         │ Contains
         ▼
┌─────────────────┐
│    Building     │
│                 │
└────────┬────────┘
         │
         │ Contains
         ▼
┌─────────────────┐
│      Unit       │
│                 │
└────────┬────────┘
         │
         │ Contains
         ▼
┌─────────────────┐
│   Scan Data     │  (Entry, Exit, Delivery)
│                 │
└─────────────────┘</pre>
                </div>

                <div class="accordion mb-4">
                    <div class="accordion-item">
                        <div class="accordion-header">
                            <span><i class="fas fa-building"></i> Complex Level</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="accordion-content">
                            <p>A <strong>Complex</strong> is the highest level in the hierarchy and is optional. It represents a collection of buildings, such as an apartment complex, shopping mall, or business park.</p>
                            <p><strong>Key attributes:</strong></p>
                            <ul>
                                <li><code>complex_id</code>: Unique identifier for the complex</li>
                                <li>Contains multiple buildings</li>
                            </ul>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <div class="accordion-header">
                            <span><i class="fas fa-building"></i> Building Level</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="accordion-content">
                            <p>A <strong>Building</strong> represents a physical structure that contains multiple units. It's the middle level in the hierarchy.</p>
                            <p><strong>Key attributes:</strong></p>
                            <ul>
                                <li><code>building_place_id</code> (BPID): Unique identifier for the building</li>
                                <li>May belong to a complex (if complex_id is present)</li>
                                <li>Contains multiple units</li>
                            </ul>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <div class="accordion-header">
                            <span><i class="fas fa-door-open"></i> Unit Level</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="accordion-content">
                            <p>A <strong>Unit</strong> represents an individual address or location within a building, such as an apartment, office, or store.</p>
                            <p><strong>Key attributes:</strong></p>
                            <ul>
                                <li><code>unit_place_id</code> (UPID): Unique identifier for the unit</li>
                                <li>Belongs to a building (building_place_id)</li>
                                <li>May have an address_id (AID)</li>
                                <li>Contains scan data</li>
                            </ul>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <div class="accordion-header">
                            <span><i class="fas fa-qrcode"></i> Scan Data</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="accordion-content">
                            <p><strong>Scan Data</strong> represents events or interactions at a unit, such as deliveries or pickups.</p>
                            <p><strong>Scan Types:</strong></p>
                            <ul>
                                <li><strong>Entry Scan</strong>: Recorded when entering a location</li>
                                <li><strong>Exit Scan</strong>: Recorded when leaving a location</li>
                                <li><strong>Delivery Scan</strong>: Recorded when making a delivery</li>
                            </ul>
                            <p><strong>Key attributes:</strong></p>
                            <ul>
                                <li><code>date</code>: Date of the scan</li>
                                <li>Coordinate columns: <code>enter_latitude</code>, <code>enter_longitude</code>, <code>exit_latitude</code>, <code>exit_longitude</code>, <code>scan_latitude</code>, <code>scan_longitude</code></li>
                                <li>May have <code>delivery_hints</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <section id="installation">
                <h2><i class="fas fa-download"></i> Installation and Setup</h2>

                <div class="card mb-4">
                    <h3>System Requirements</h3>
                    <p>Before installing GeoDAD, ensure your system meets the following requirements:</p>

                    <ul>
                        <li><strong>Python 3.8 or higher</strong>: The application is built with Python and requires version 3.8+</li>
                        <li><strong>Modern web browser</strong>: Chrome, Firefox, or Edge is recommended for the best experience</li>
                        <li><strong>Minimum 4GB RAM</strong>: For processing larger datasets</li>
                        <li><strong>500MB disk space</strong>: For the application and its dependencies</li>
                    </ul>
                </div>

                <div class="card mb-4">
                    <h3>Installation Steps</h3>
                    <p>Follow these steps to install and set up the GeoDAD system:</p>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-code-branch"></i> Step 1: Clone the Repository</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Clone the GeoDAD repository to your local machine:</p>
                                <pre><code>git clone &lt;repository-url&gt;
cd geographic_data_analysis</code></pre>
                                <p>Replace <code>&lt;repository-url&gt;</code> with the actual URL of the repository.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-vial"></i> Step 2: Create a Virtual Environment</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Create a virtual environment to isolate the project dependencies:</p>
                                <pre><code>python -m venv venv</code></pre>
                                <p>This creates a new virtual environment in the <code>venv</code> directory.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-power-off"></i> Step 3: Activate the Virtual Environment</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Activate the virtual environment:</p>
                                <p><strong>Windows:</strong></p>
                                <pre><code>venv\Scripts\activate</code></pre>
                                <p><strong>macOS/Linux:</strong></p>
                                <pre><code>source venv/bin/activate</code></pre>
                                <p>You should see the virtual environment name in your command prompt, indicating it's active.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-box"></i> Step 4: Install Dependencies</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Install the required packages using pip:</p>
                                <pre><code>pip install -r requirements.txt</code></pre>
                                <p>This will install all the necessary dependencies listed in the <code>requirements.txt</code> file.</p>
                                <div class="info-box">
                                    <h4>Key Dependencies</h4>
                                    <ul>
                                        <li><strong>dash</strong>: Web application framework</li>
                                        <li><strong>pandas</strong>: Data processing and analysis</li>
                                        <li><strong>plotly</strong>: Interactive visualizations</li>
                                        <li><strong>dash-leaflet</strong>: Interactive maps</li>
                                        <li><strong>scikit-learn</strong>: Machine learning for spatial analysis</li>
                                        <li><strong>reportlab</strong>: PDF report generation</li>
                                        <li><strong>geopandas</strong>: Geographic data processing</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-play"></i> Step 5: Run the Application</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Start the GeoDAD application:</p>
                                <pre><code>python app.py</code></pre>
                                <p>The application will be available at <a href="http://127.0.0.1:8050/" target="_blank">http://127.0.0.1:8050/</a> in your web browser.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <h3>Project Structure</h3>
                    <p>The GeoDAD project follows a modular structure with clear separation of concerns:</p>

                    <pre><code>geographic_data_analysis/
├── app.py                       # Main application file and entry point
├── data_processor.py            # Data validation and cleaning module
├── visualization.py             # Statistical visualizations and charts
├── map_handler.py               # Interactive mapping and geospatial visualization
├── report_generator.py          # PDF report generation and export
├── Unit_Analyzer.py             # Spatial analysis and pattern detection
├── enhanced_cleaning_report.py  # Detailed cleaning reports generation
├── requirements.txt             # Project dependencies
├── Dockerfile                   # Docker configuration for containerization
├── .gitignore                   # Git ignore configuration
├── LICENSE                      # Project license information
├── README.md                    # Project documentation (this file)
├── documentation.bat            # Quick access to documentation
├── assets/                      # CSS and static assets
│   ├── custom.css               # Custom styling for the application
│   ├── favicon.ico              # Application favicon
│   └── images/                  # Static images for the application
├── tests/                       # Unit and integration tests
│   ├── test_data_processor.py   # Tests for data processing module
│   ├── test_visualization.py    # Tests for visualization module
│   ├── test_map_handler.py      # Tests for map handling module
│   └── test_unit_analyzer.py    # Tests for spatial analysis module
└── Documentation/               # Comprehensive project documentation
    ├── GeoDAD_Project_Playbook_Enhanced.html  # Main documentation file
    ├── index.html                             # Documentation hub
    ├── custom.css                             # Custom styling for documentation
    ├── header.html                            # HTML header for documentation
    └── images/                                # Diagrams and images
        ├── architecture_diagram.svg           # System architecture diagram
        ├── data_flow_diagram.svg              # Data processing flow diagram
        ├── hierarchical_treemap.svg           # Treemap visualization diagram
        └── dashboard_screenshot.png           # Application screenshot</code></pre>
                </div>

                <div class="card mb-4">
                    <h3>Troubleshooting Installation</h3>
                    <p>If you encounter issues during installation, try these solutions:</p>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Dependency Installation Errors</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Issue:</strong> Errors when installing dependencies with pip.</p>
                                <p><strong>Solution:</strong></p>
                                <ul>
                                    <li>Ensure you have the latest version of pip: <code>python -m pip install --upgrade pip</code></li>
                                    <li>Install dependencies one by one to identify problematic packages</li>
                                    <li>Check for platform-specific requirements (e.g., some packages may need additional system libraries)</li>
                                </ul>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Application Startup Errors</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Issue:</strong> Errors when starting the application with <code>python app.py</code>.</p>
                                <p><strong>Solution:</strong></p>
                                <ul>
                                    <li>Check that all dependencies are installed correctly</li>
                                    <li>Ensure you're running the command from the project root directory</li>
                                    <li>Check for port conflicts (the application uses port 8050 by default)</li>
                                </ul>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Browser Display Issues</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Issue:</strong> The application doesn't display correctly in the browser.</p>
                                <p><strong>Solution:</strong></p>
                                <ul>
                                    <li>Try a different browser (Chrome, Firefox, or Edge is recommended)</li>
                                    <li>Clear your browser cache and cookies</li>
                                    <li>Check the browser console for JavaScript errors</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4" id="deployment">
                    <h3><i class="fas fa-rocket"></i> Deployment Guide</h3>
                    <p>This section provides instructions for deploying the GeoDAD application in various environments.</p>

                    <h4>Development Environment</h4>
                    <p>For local development, simply run the application using:</p>
                    <pre><code>python app.py</code></pre>
                    <p>The application will be available at <a href="http://127.0.0.1:8050/" target="_blank">http://127.0.0.1:8050/</a>.</p>

                    <h4>Production Deployment with Gunicorn</h4>
                    <p>For production deployment, we recommend using Gunicorn with a reverse proxy like Nginx:</p>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-server"></i> Step 1: Install Gunicorn</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Install Gunicorn using pip:</p>
                                <pre><code>pip install gunicorn</code></pre>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-play"></i> Step 2: Run with Gunicorn</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Run the application with Gunicorn:</p>
                                <pre><code>gunicorn --workers=4 --threads=2 --bind=0.0.0.0:8050 app:server</code></pre>
                                <p>This command:</p>
                                <ul>
                                    <li>Starts 4 worker processes</li>
                                    <li>Uses 2 threads per worker</li>
                                    <li>Binds to all network interfaces on port 8050</li>
                                    <li>Uses the <code>server</code> object from <code>app.py</code></li>
                                </ul>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-cogs"></i> Step 3: Configure Nginx as a Reverse Proxy</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Create an Nginx configuration file (e.g., <code>/etc/nginx/sites-available/geodad</code>):</p>
                                <pre><code>server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8050;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}</code></pre>
                                <p>Enable the site and restart Nginx:</p>
                                <pre><code>sudo ln -s /etc/nginx/sites-available/geodad /etc/nginx/sites-enabled/
sudo systemctl restart nginx</code></pre>
                            </div>
                        </div>
                    </div>

                    <h4>Docker Deployment</h4>
                    <p>For containerized deployment, use Docker:</p>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-docker"></i> Step 1: Build the Docker Image</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Build the Docker image using the provided Dockerfile:</p>
                                <pre><code>docker build -t geodad .</code></pre>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-play"></i> Step 2: Run the Container</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Run the Docker container:</p>
                                <pre><code>docker run -p 8050:8050 geodad</code></pre>
                                <p>The application will be available at <a href="http://localhost:8050/" target="_blank">http://localhost:8050/</a>.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-cloud"></i> Step 3: Deploy to Cloud Services</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The Docker image can be deployed to various cloud services:</p>
                                <ul>
                                    <li><strong>AWS ECS</strong>: Push the image to ECR and deploy as an ECS service</li>
                                    <li><strong>Google Cloud Run</strong>: Push the image to GCR and deploy as a Cloud Run service</li>
                                    <li><strong>Azure Container Instances</strong>: Push the image to ACR and deploy as a container instance</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-shield-alt"></i> Security Considerations</h4>
                        <p>When deploying to production, consider these security measures:</p>
                        <ul>
                            <li>Use HTTPS with a valid SSL certificate</li>
                            <li>Implement authentication if the application contains sensitive data</li>
                            <li>Set up proper firewall rules to restrict access</li>
                            <li>Regularly update dependencies to patch security vulnerabilities</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="how-to-use">
                <h2><i class="fas fa-book-reader"></i> How to Use This Playbook</h2>

                <div class="card mb-4">
                    <h3>Navigation Tips</h3>
                    <p>This playbook is organized into sections that you can navigate using the sidebar on the left. Here are some tips for getting the most out of this documentation:</p>

                    <ul>
                        <li><strong>Start with the System Overview</strong> to get a high-level understanding of the GeoDAD architecture</li>
                        <li><strong>Explore the Core Modules</strong> to learn about the main components of the system</li>
                        <li><strong>Dive into the Data Processing Pipeline</strong> to understand how data flows through the system</li>
                        <li><strong>Check out the Visualization Components</strong> to see how data is presented to users</li>
                        <li><strong>Refer to the Appendices</strong> for detailed reference information</li>
                    </ul>
                </div>

                <div class="info-box mb-4">
                    <h4><i class="fas fa-users"></i> For Different Audiences</h4>

                    <div class="tabs">
                        <ul class="tab-nav">
                            <li class="tab-nav-item active" data-tab="business">Business Stakeholders</li>
                            <li class="tab-nav-item" data-tab="developers">Developers</li>
                            <li class="tab-nav-item" data-tab="analysts">Data Analysts</li>
                            <li class="tab-nav-item" data-tab="beginners">Beginners</li>
                        </ul>

                        <div class="tab-content">
                            <div class="tab-pane active" data-tab="business">
                                <p><strong>For Business Stakeholders:</strong></p>
                                <ul>
                                    <li>Start with the <a href="#introduction">Introduction</a> and <a href="#system-overview">System Overview</a></li>
                                    <li>Focus on the <a href="#key-features">Key Features</a> section to understand capabilities</li>
                                    <li>Review the <a href="#user-interaction">User Interaction</a> section to understand the user experience</li>
                                    <li>Check out the <a href="#hierarchical-treemap">Visualization Components</a> to see how data is presented</li>
                                </ul>
                            </div>

                            <div class="tab-pane" data-tab="developers">
                                <p><strong>For Developers:</strong></p>
                                <ul>
                                    <li>Review the <a href="#system-architecture">System Architecture</a> to understand the overall structure</li>
                                    <li>Dive into the <a href="#core-modules">Core Modules</a> section for implementation details</li>
                                    <li>Study the <a href="#user-interaction">Callback Structure</a> to understand how components interact</li>
                                    <li>Refer to the <a href="#code-reference">Code Reference</a> for specific implementation details</li>
                                </ul>
                            </div>

                            <div class="tab-pane" data-tab="analysts">
                                <p><strong>For Data Analysts:</strong></p>
                                <ul>
                                    <li>Focus on the <a href="#data-structure">Data Structure</a> section to understand the data model</li>
                                    <li>Review the <a href="#data-processing-pipeline">Data Processing Pipeline</a> to understand how data is processed</li>
                                    <li>Explore the <a href="#hierarchical-treemap">Visualization Components</a> to see how data is visualized</li>
                                    <li>Check out the <a href="#spatial-analysis">Spatial Analysis</a> section for advanced analysis techniques</li>
                                </ul>
                            </div>

                            <div class="tab-pane" data-tab="beginners">
                                <p><strong>For Beginners:</strong></p>
                                <ul>
                                    <li>Start with the <a href="#introduction">Introduction</a> to get a basic understanding</li>
                                    <li>Read through the <a href="#how-to-use">How to Use This Playbook</a> section (you're here now!)</li>
                                    <li>Look at the <a href="#data-structure">Data Structure</a> section to understand the basic concepts</li>
                                    <li>Follow the <a href="#user-interaction-flow">User Interaction Flow</a> to see how the system is used</li>
                                    <li>Refer to the <a href="#glossary">Glossary</a> for unfamiliar terms</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tip-box">
                    <h4><i class="fas fa-lightbulb"></i> Pro Tips</h4>
                    <ul>
                        <li><strong>Interactive Elements:</strong> Look for accordions, tabs, and other interactive elements that provide additional information</li>
                        <li><strong>Code Examples:</strong> Code snippets are provided to illustrate key concepts</li>
                        <li><strong>Diagrams:</strong> Visual diagrams help explain complex relationships</li>
                        <li><strong>Info Boxes:</strong> Special boxes highlight important information</li>
                    </ul>
                </div>
            </section>

            <section id="system-architecture">
                <h2><i class="fas fa-project-diagram"></i> System Architecture</h2>

                <div class="card mb-4">
                    <h3>Component-Based Architecture</h3>
                    <p>GeoDAD follows a modular, component-based architecture with clear separation of concerns. This makes the system easy to maintain, extend, and understand.</p>

                    <div class="diagram-container">
                        <img src="images/architecture_diagram_detailed.svg" alt="GeoDAD Architecture Diagram" class="diagram-image">
                        <p class="diagram-caption">Figure 1: Detailed architecture of the GeoDAD system showing all modules and their key components</p>
                    </div>

                    <p>The system is organized into the following core modules:</p>

                    <div class="ascii-diagram">
<span class="module-data-processor">app.py</span> (Main Controller)
├── <span class="module-data-processor">data_processor.py</span> (Data Validation/Cleaning)
├── <span class="module-visualization">visualization.py</span> (Statistical Charts/Graphs)
├── <span class="module-map-handler">map_handler.py</span> (Interactive Mapping)
├── <span class="module-report">report_generator.py</span> (PDF Reporting)
├── <span class="module-analyzer">Unit_Analyzer.py</span> (Spatial Analysis)
└── <span class="module-data-processor">enhanced_cleaning_report.py</span> (Cleaning Reports)</div>
                </div>

                <div class="accordion mb-4">
                    <div class="accordion-item">
                        <div class="accordion-header">
                            <span><i class="fas fa-puzzle-piece"></i> Modular Design Benefits</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="accordion-content">
                            <p>The modular design of GeoDAD provides several key benefits:</p>
                            <ul>
                                <li><strong>Maintainability</strong>: Each module has a single responsibility, making it easier to maintain and update</li>
                                <li><strong>Extensibility</strong>: New features can be added by creating new modules or extending existing ones</li>
                                <li><strong>Testability</strong>: Modules can be tested independently, making it easier to ensure quality</li>
                                <li><strong>Collaboration</strong>: Different team members can work on different modules simultaneously</li>
                                <li><strong>Reusability</strong>: Modules can be reused in other projects or contexts</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <section id="data-flow">
                <h2><i class="fas fa-exchange-alt"></i> Data Flow</h2>

                <div class="card mb-4">
                    <h3>Data Flow Diagram</h3>
                    <p>The diagram below illustrates how data flows through the GeoDAD system, from initial upload to final visualization and reporting:</p>

                    <div class="diagram-container">
                        <img src="images/data_flow_diagram_detailed.svg" alt="GeoDAD Data Flow Diagram" class="diagram-image">
                        <p class="diagram-caption">Figure 2: Detailed data flow through the GeoDAD system showing all processing steps</p>
                    </div>

                    <div class="ascii-diagram">
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   CSV Upload    │────▶│      Data       │────▶│    Cleaned      │
│                 │     │   Validation    │     │     Data        │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│      PDF        │◄────│     Report      │◄────│    Analytics    │
│     Report      │     │   Generation    │     │     Engine      │
└─────────────────┘     └─────────────────┘     └─────────┬───────┘
                                                         │
                    ┌─────────────────┐                  │
                    │   Interactive   │◄─────────────────┘
                    │       Map       │
                    └────────┬────────┘
                             │
                             │
                             ▼
                    ┌─────────────────┐
                    │   Statistical    │
                    │  Visualizations  │
                    └─────────────────┘</div>
                </div>

                <div class="info-box mb-4">
                    <h4><i class="fas fa-info-circle"></i> Data Flow Steps</h4>
                    <ol>
                        <li><strong>CSV Upload</strong>: User uploads a CSV file containing geographic data</li>
                        <li><strong>Data Validation</strong>: System validates the data structure and required columns</li>
                        <li><strong>Data Cleaning</strong>: System cleans the data by handling missing values and calculating missing centroids</li>
                        <li><strong>Analytics Engine</strong>: System processes the cleaned data for analysis</li>
                        <li><strong>Interactive Map</strong>: System generates an interactive map with the processed data</li>
                        <li><strong>Statistical Visualizations</strong>: System generates statistical visualizations based on the map data</li>
                        <li><strong>Report Generation</strong>: System generates a comprehensive PDF report with the analysis results</li>
                    </ol>
                </div>
            </section>

            <section id="user-interaction">
                <h2><i class="fas fa-users"></i> User Interaction</h2>

                <div class="card mb-4">
                    <h3>User Interface Components</h3>
                    <p>The GeoDAD user interface is designed to be intuitive and easy to use. It consists of the following main components:</p>

                    <div class="ascii-diagram">
Dashboard Container
├── Title Section
├── Upload Section
├── Control Buttons
├── Status Display
├── Map Container
│   ├── Control Panel
│   └── Interactive Map
├── Treemap Container
└── Other Visualizations Container
    ├── Filter Controls
    └── Visualization Content</div>
                </div>

                <div class="card mb-4">
                    <h3>Callback Relationship Diagram</h3>
                    <p>The diagram below illustrates how callbacks connect UI components, processing functions, and output components in the GeoDAD system:</p>

                    <div class="diagram-container">
                        <img src="images/callback_relationship_diagram.svg" alt="GeoDAD Callback Relationship Diagram" class="diagram-image">
                        <p class="diagram-caption">Figure 4: Detailed callback relationship diagram showing inputs, processing, and outputs</p>
                    </div>
                </div>

                <div class="card mb-4">
                    <h3>User Interaction Flow</h3>
                    <p>The typical user interaction flow in GeoDAD follows these steps:</p>

                    <div class="tabs">
                        <ul class="tab-nav">
                            <li class="tab-nav-item active" data-tab="upload">1. Data Upload</li>
                            <li class="tab-nav-item" data-tab="clean">2. Data Cleaning</li>
                            <li class="tab-nav-item" data-tab="visualize">3. Visualization</li>
                            <li class="tab-nav-item" data-tab="analyze">4. Analysis</li>
                            <li class="tab-nav-item" data-tab="report">5. Reporting</li>
                        </ul>

                        <div class="tab-content">
                            <div class="tab-pane active" data-tab="upload">
                                <h4>Step 1: Data Upload</h4>
                                <p>The user starts by uploading a CSV file containing geographic data:</p>
                                <ol>
                                    <li>Click on the upload area or drag and drop a CSV file</li>
                                    <li>The system validates the file format and structure</li>
                                    <li>A summary of the uploaded data is displayed</li>
                                    <li>The user can review the data before proceeding</li>
                                </ol>
                                <div class="tip-box">
                                    <p><strong>Tip:</strong> Make sure your CSV file includes the required columns: <code>unit_place_id</code>, <code>building_place_id</code>, <code>date</code>, and coordinate columns.</p>
                                </div>
                            </div>

                            <div class="tab-pane" data-tab="clean">
                                <h4>Step 2: Data Cleaning</h4>
                                <p>Once the data is uploaded, the user can clean it:</p>
                                <ol>
                                    <li>Click the "Clean the input file" button</li>
                                    <li>The system processes the data, handling missing values and calculating missing centroids</li>
                                    <li>A cleaning report is displayed, showing what was cleaned and how</li>
                                    <li>The user can review the cleaning results before proceeding</li>
                                </ol>
                                <div class="info-box">
                                    <p><strong>Note:</strong> The cleaning process is automatic and handles common issues like missing coordinates, invalid data formats, and duplicate entries.</p>
                                </div>
                            </div>

                            <div class="tab-pane" data-tab="visualize">
                                <h4>Step 3: Visualization</h4>
                                <p>After cleaning the data, the user can visualize it:</p>
                                <ol>
                                    <li>Click the "Show the Cleaned Data on Map" button</li>
                                    <li>The system generates an interactive map with the cleaned data</li>
                                    <li>The user can explore the map, toggle layers, and filter the data</li>
                                    <li>Additional visualizations like the hierarchical treemap can be generated</li>
                                </ol>
                                <div class="tip-box">
                                    <p><strong>Tip:</strong> Use the layer controls to toggle different types of data (entry, exit, delivery scans) on and off.</p>
                                </div>
                            </div>

                            <div class="tab-pane" data-tab="analyze">
                                <h4>Step 4: Analysis</h4>
                                <p>The user can perform various analyses on the data:</p>
                                <ol>
                                    <li>Use the hierarchical treemap to explore relationships between complexes, buildings, and units</li>
                                    <li>Click on entities in the treemap to filter other visualizations</li>
                                    <li>View statistical charts and graphs to understand patterns in the data</li>
                                    <li>Use the spatial analysis tools to identify clusters and outliers</li>
                                </ol>
                                <div class="info-box">
                                    <p><strong>Note:</strong> The hierarchical treemap is the central control mechanism that synchronizes with all other visualizations.</p>
                                </div>
                            </div>

                            <div class="tab-pane" data-tab="report">
                                <h4>Step 5: Reporting</h4>
                                <p>Finally, the user can generate a comprehensive report:</p>
                                <ol>
                                    <li>Click the "Generate EDA Report" button</li>
                                    <li>The system creates a PDF report with detailed analysis</li>
                                    <li>The report includes summary statistics, data quality metrics, and visualization placeholders</li>
                                    <li>The user can download and share the report</li>
                                </ol>
                                <div class="tip-box">
                                    <p><strong>Tip:</strong> The PDF report is a great way to share your findings with stakeholders who don't have access to the GeoDAD system.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="warning-box">
                    <h4><i class="fas fa-exclamation-triangle"></i> Important Note</h4>
                    <p>The hierarchical treemap should remain visible at all times since it functions as the central filtering mechanism for all other visualizations in the dashboard.</p>
                </div>
            </section>

            <section id="core-modules">
                <h2><i class="fas fa-cubes"></i> Core Modules</h2>
                <p>The GeoDAD system is built around several core modules, each with a specific responsibility. This section provides a detailed explanation of each module.</p>

                <div id="app-controller" class="card mb-4">
                    <h3><i class="fas fa-brain"></i> app.py (Main Controller)</h3>
                    <p>The central controller of the application that coordinates all other modules and manages the user interface.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Initializing the Dash application</li>
                            <li>Defining the UI layout</li>
                            <li>Managing callbacks for interactive components</li>
                            <li>Coordinating data flow between modules</li>
                        </ul>
                    </div>

                    <h4>Key Components</h4>
                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>App Initialization</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The app initialization process sets up the Dash application with external stylesheets and scripts:</p>
                                <pre><code>app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.BOOTSTRAP],
    suppress_callback_exceptions=True
)

server = app.server</code></pre>
                                <p>This creates a new Dash application with Bootstrap styling and enables callback exceptions to be suppressed, which is useful for complex applications with dynamic layouts.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Layout Definition</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The layout definition creates the structure of the user interface:</p>
                                <pre><code>app.layout = html.Div([
    # Title and header
    html.H1("Geographic Data Analysis Dashboard"),

    # Upload section
    dcc.Upload(id="upload-data", children=[...]),

    # Control buttons
    html.Div([
        html.Button("Clean the input file", id="clean-button"),
        html.Button("Show the Cleaned Data on Map", id="show-map-button"),
        html.Button("Generate EDA Report", id="generate-report-button")
    ]),

    # Status display
    html.Div(id="output-data-upload"),

    # Map container
    html.Div(id="map-container"),

    # Treemap container
    html.Div(id="treemap-container"),

    # Other visualizations container
    html.Div(id="visualizations-container"),

    # Hidden components for storing data
    dcc.Store(id="stored-data"),
    dcc.Store(id="cleaned-data"),
    dcc.Store(id="filter-store")
])</code></pre>
                                <p>This creates a layout with sections for uploading data, controlling the application, displaying status messages, and showing visualizations.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Callback Management</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Callbacks connect user interactions to application functions:</p>
                                <pre><code>@app.callback(
    Output("output-data-upload", "children"),
    Input("upload-data", "contents"),
    State("upload-data", "filename"),
    State("upload-data", "last_modified")
)
def parse_contents(contents, filename, date):
    if contents is not None:
        # Process the uploaded file
        df = data_processor.parse_upload(contents, filename)

        # Validate the data
        validation_result = data_processor.validate_data(df)

        # Display the validation result
        return validation_result

    return html.Div("Upload a CSV file to begin.")</code></pre>
                                <p>This callback processes uploaded files, validates the data, and displays the results to the user.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Error Handling</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Error handling ensures that the application provides useful feedback when things go wrong:</p>
                                <pre><code>def handle_error(error_message):
    return html.Div([
        html.H4("Error", className="error-title"),
        html.P(error_message, className="error-message"),
        html.P("Please check your input and try again.")
    ], className="error-container")</code></pre>
                                <p>This function creates a user-friendly error message that explains what went wrong and how to fix it.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="data-processor" class="card mb-4">
                    <h3><i class="fas fa-filter"></i> data_processor.py (Data Validation/Cleaning)</h3>
                    <p>Handles all aspects of data processing, including validation, cleaning, and preparation for analysis.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Validating data structure and required columns</li>
                            <li>Cleaning null values and invalid data</li>
                            <li>Calculating missing centroids</li>
                            <li>Generating cleaning reports</li>
                        </ul>
                    </div>

                    <h4>Key Functions</h4>
                    <ul>
                        <li><code>validate_data(df)</code>: Checks if the uploaded data has the required structure</li>
                        <li><code>clean_data(df)</code>: Performs data cleaning operations</li>
                        <li><code>handle_nan_values(df)</code>: Handles missing values in coordinate columns</li>
                        <li><code>calculate_missing_centroids(df)</code>: Calculates centroids for units with missing coordinates</li>
                    </ul>

                    <div class="diagram-container">
                        <img src="images/data_processor_class_diagram.svg" alt="DataProcessor Class Diagram" class="diagram-image">
                        <p class="diagram-caption">Figure 5: Class diagram of the DataProcessor showing attributes and methods</p>
                    </div>

                    <div class="tip-box">
                        <h4>Data Validation Process</h4>
                        <p>The data validation process checks for:</p>
                        <ol>
                            <li>Required columns: <code>unit_place_id</code>, <code>building_place_id</code>, <code>date</code>, and coordinate columns</li>
                            <li>Data types: Ensures coordinates are numeric, dates are in the correct format, etc.</li>
                            <li>Data integrity: Checks for duplicate entries, inconsistent hierarchies, etc.</li>
                        </ol>
                        <p>If any issues are found, the system provides detailed feedback to help the user fix the problems.</p>
                    </div>
                </div>

                <div id="visualization" class="card mb-4">
                    <h3><i class="fas fa-chart-line"></i> visualization.py (Statistical Charts/Graphs)</h3>
                    <p>Creates interactive visualizations for data analysis, helping users understand patterns and relationships in the data.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Creating histograms for distribution analysis</li>
                            <li>Generating scatter plots for relationship analysis</li>
                            <li>Building correlation heatmaps for variable relationships</li>
                            <li>Developing 3D visualizations for spatial-temporal patterns</li>
                            <li>Creating hierarchical treemaps for entity relationships</li>
                        </ul>
                    </div>

                    <h4>Key Functions</h4>
                    <ul>
                        <li><code>create_histograms(df, columns)</code>: Generates histograms for numeric columns</li>
                        <li><code>create_scatter_plots(df, x_col, y_col)</code>: Creates scatter plots for coordinate data</li>
                        <li><code>create_correlation_heatmap(df)</code>: Generates correlation matrix visualization</li>
                        <li><code>create_hierarchical_treemap(df)</code>: Creates interactive treemap visualization</li>
                        <li><code>create_3d_scatter(df)</code>: Generates 3D scatter plot for spatial-temporal analysis</li>
                        <li><code>create_animated_time_series(df)</code>: Creates animated time series visualization</li>
                    </ul>

                    <div class="diagram-container">
                        <img src="images/hierarchical_treemap_detailed.svg" alt="Hierarchical Treemap Visualization" class="diagram-image">
                        <p class="diagram-caption">Figure 3: Detailed example of a hierarchical treemap visualization showing complex, building, and unit levels with scan counts</p>
                    </div>
                </div>

                <div id="map-handler" class="card mb-4">
                    <h3><i class="fas fa-map-marked-alt"></i> map_handler.py (Interactive Mapping)</h3>
                    <p>Manages all map-related functionality, providing interactive maps for exploring geographic data. This module is the core of the spatial visualization capabilities in GeoDAD.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Initializing interactive maps with appropriate base layers</li>
                            <li>Adding data layers for entry, exit, and delivery scans</li>
                            <li>Handling map controls and interactions</li>
                            <li>Integrating road data with customizable styles</li>
                            <li>Managing marker clustering for improved performance</li>
                            <li>Creating informative popups for map elements</li>
                            <li>Synchronizing map with other visualizations</li>
                        </ul>
                    </div>

                    <h4>Module Structure</h4>
                    <p>The map_handler.py module is structured as follows:</p>

                    <pre><code>import dash_html_components as html
import dash_leaflet as dl
import json
import pandas as pd
from shapely.geometry import Point
import geopandas as gpd
from dash_leaflet import express as dlx

# Constants for map styling
BASE_STYLE = {...}
HIGHLIGHT_STYLE = {...}
ROAD_STYLES = {...}

class MapHandler:
    def __init__(self):
        self.map_id = "map"
        self.default_zoom = 13
        self.default_center = [37.7749, -122.4194]  # Default center (San Francisco)
        self.roads_gdf = None
        self.buildings_gdf = None

    def initialize_map(self, center=None, zoom=None):
        """Initialize the map with base layers and controls."""
        # Implementation details...

    def update_map_data(self, df, filter_by=None):
        """Update the map with new data, optionally filtered."""
        # Implementation details...

    def add_road_endpoints(self, df):
        """Add road entry points to the map."""
        # Implementation details...

    def create_popup_content(self, point_data):
        """Generate HTML content for popups."""
        # Implementation details...

    def load_roads_data(self, geojson_path):
        """Load road data from a GeoJSON file."""
        # Implementation details...

    def style_roads(self, feature):
        """Apply styling to road features based on their type."""
        # Implementation details...

    def create_marker_clusters(self, df):
        """Create marker clusters for improved performance."""
        # Implementation details...</code></pre>

                    <h4>Key Functions - Detailed Explanation</h4>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><code>initialize_map(center=None, zoom=None)</code></span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Purpose:</strong> Creates a new Leaflet map with base layers and controls.</p>
                                <p><strong>Parameters:</strong></p>
                                <ul>
                                    <li><code>center</code> (list, optional): Center coordinates [lat, lng] for the map. Defaults to self.default_center.</li>
                                    <li><code>zoom</code> (int, optional): Initial zoom level. Defaults to self.default_zoom.</li>
                                </ul>
                                <p><strong>Returns:</strong> A dash_leaflet.Map component configured with base layers and controls.</p>
                                <p><strong>Implementation Details:</strong></p>
                                <pre><code>def initialize_map(self, center=None, zoom=None):
    """Initialize the map with base layers and controls."""
    if center is None:
        center = self.default_center
    if zoom is None:
        zoom = self.default_zoom

    # Create base map layers
    satellite = dl.TileLayer(
        url="https://{s}.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}",
        maxZoom=20,
        subdomains=["mt0", "mt1", "mt2", "mt3"],
        attribution="Google"
    )

    streets = dl.TileLayer(
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        maxZoom=19,
        attribution="&copy; OpenStreetMap contributors"
    )

    # Create layer control
    layer_control = dl.LayersControl(
        [dl.BaseLayer(satellite, name="Satellite", checked=True),
         dl.BaseLayer(streets, name="Streets")]
    )

    # Create the map
    map_component = dl.Map(
        id=self.map_id,
        center=center,
        zoom=zoom,
        children=[satellite, layer_control],
        style={"width": "100%", "height": "70vh"}
    )

    return map_component</code></pre>
                                <p>This function creates a Leaflet map with satellite and street base layers, and adds a layer control for switching between them. The map is centered at the specified coordinates (or defaults to San Francisco) and zoomed to the specified level.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><code>update_map_data(df, filter_by=None)</code></span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Purpose:</strong> Updates the map with new data, optionally filtered by complex, building, or unit.</p>
                                <p><strong>Parameters:</strong></p>
                                <ul>
                                    <li><code>df</code> (DataFrame): DataFrame containing the data to display on the map.</li>
                                    <li><code>filter_by</code> (dict, optional): Dictionary with filter criteria (e.g., {"complex_id": "C123"}).</li>
                                </ul>
                                <p><strong>Returns:</strong> A list of map layers to add to the map.</p>
                                <p><strong>Implementation Details:</strong></p>
                                <pre><code>def update_map_data(self, df, filter_by=None):
    """Update the map with new data, optionally filtered."""
    # Apply filters if specified
    if filter_by is not None:
        for key, value in filter_by.items():
            if key in df.columns and value is not None:
                df = df[df[key] == value]

    # Create marker clusters for each scan type
    entry_cluster = self.create_marker_cluster(df, "entry", "#3498db")
    exit_cluster = self.create_marker_cluster(df, "exit", "#e74c3c")
    delivery_cluster = self.create_marker_cluster(df, "delivery", "#2ecc71")

    # Create overlay controls for the clusters
    overlay_control = dl.LayersControl(
        overlays=[
            dl.Overlay(entry_cluster, name="Entry Scans", checked=True),
            dl.Overlay(exit_cluster, name="Exit Scans", checked=True),
            dl.Overlay(delivery_cluster, name="Delivery Scans", checked=True)
        ]
    )

    # Add roads layer if available
    if self.roads_gdf is not None:
        roads_layer = dl.GeoJSON(
            data=json.loads(self.roads_gdf.to_json()),
            options=dict(style=self.style_roads),
            id="roads-layer"
        )
        overlay_control.overlays.append(dl.Overlay(roads_layer, name="Roads", checked=True))

    # Calculate map bounds to fit all points
    if not df.empty:
        bounds = [
            [df["latitude"].min(), df["longitude"].min()],
            [df["latitude"].max(), df["longitude"].max()]
        ]
        bounds_component = dl.FitBounds(bounds=bounds)
    else:
        bounds_component = None

    return [entry_cluster, exit_cluster, delivery_cluster, overlay_control, bounds_component]</code></pre>
                                <p>This function filters the data based on the specified criteria, creates marker clusters for each scan type (entry, exit, delivery), adds a roads layer if available, and calculates the map bounds to fit all points. It returns a list of components to add to the map.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><code>create_popup_content(point_data)</code></span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Purpose:</strong> Generates HTML content for popups that appear when clicking on map markers.</p>
                                <p><strong>Parameters:</strong></p>
                                <ul>
                                    <li><code>point_data</code> (dict): Dictionary containing data for a single point.</li>
                                </ul>
                                <p><strong>Returns:</strong> HTML string for the popup content.</p>
                                <p><strong>Implementation Details:</strong></p>
                                <pre><code>def create_popup_content(self, point_data):
    """Generate HTML content for popups."""
    # Start with basic information
    content = "<div class='popup-content'>\n"
    content += "<h4>Scan Details</h4>\n"
    content += f"<p><strong>Scan Type:</strong> {point_data['scan_type'].capitalize()}</p>\n"
    content += f"<p><strong>Date:</strong> {point_data['date']}</p>\n"

    # Add building and unit information
    content += f"<p><strong>Building ID:</strong> {point_data['building_place_id']}</p>\n"
    content += f"<p><strong>Unit ID:</strong> {point_data['unit_place_id']}</p>\n"

    # Add complex information if available
    if 'complex_id' in point_data and pd.notna(point_data['complex_id']):
        content += f"<p><strong>Complex ID:</strong> {point_data['complex_id']}</p>\n"

    # Add address information if available
    if 'address_id' in point_data and pd.notna(point_data['address_id']):
        content += f"<p><strong>Address ID:</strong> {point_data['address_id']}</p>\n"

    # Add delivery hints if available
    if 'delivery_hints' in point_data and pd.notna(point_data['delivery_hints']):
        content += f"<p><strong>Delivery Hints:</strong> {point_data['delivery_hints']}</p>\n"

    # Add coordinates
    content += f"<p><strong>Coordinates:</strong> [{point_data['latitude']:.6f}, {point_data['longitude']:.6f}]</p>\n"

    # Add copy button (simplified for documentation)
    content += "<button class='copy-coords-btn'>Copy Coordinates</button>\n"

    content += "</div>"
    return content</code></pre>
                                <p>This function creates a formatted HTML popup with detailed information about a scan point, including scan type, date, building and unit IDs, complex ID (if available), address ID (if available), delivery hints (if available), and coordinates. It also adds a button to copy the coordinates to the clipboard.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><code>style_roads(feature)</code></span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><strong>Purpose:</strong> Applies styling to road features based on their type.</p>
                                <p><strong>Parameters:</strong></p>
                                <ul>
                                    <li><code>feature</code> (dict): GeoJSON feature representing a road.</li>
                                </ul>
                                <p><strong>Returns:</strong> Dictionary with styling properties for the road.</p>
                                <p><strong>Implementation Details:</strong></p>
                                <pre><code>def style_roads(self, feature):
    """Apply styling to road features based on their type."""
    road_type = feature.get("properties", {}).get("highway", "other")

    if road_type == "service":
        return {
            "color": "#ffffff",  # White for service roads
            "weight": 3,
            "opacity": 0.8
        }
    elif road_type == "residential":
        return {
            "color": "#f0f0f0",  # Light gray for residential roads
            "weight": 2,
            "opacity": 0.7
        }
    elif road_type in ["primary", "secondary", "tertiary"]:
        return {
            "color": "#d3d3d3",  # Medium gray for main roads
            "weight": 4,
            "opacity": 0.9
        }
    else:
        return {
            "color": "#cccccc",  # Default gray for other roads
            "weight": 1,
            "opacity": 0.6
        }</code></pre>
                                <p>This function applies different styling to roads based on their type. Service roads are white with a weight of 3, residential roads are light gray with a weight of 2, main roads (primary, secondary, tertiary) are medium gray with a weight of 4, and other roads are light gray with a weight of 1. This makes it easy to distinguish between different types of roads on the map.</p>
                            </div>
                        </div>
                    </div>

                    <div class="warning-box">
                        <h4>Important Note</h4>
                        <p>The map_handler module is one of the most important components of the project. It should not be modified without careful consideration, as it contains complex logic for handling geographic data and interactive maps.</p>
                    </div>
                </div>

                <div id="report-generator" class="card mb-4">
                    <h3><i class="fas fa-file-pdf"></i> report_generator.py (PDF Reporting)</h3>
                    <p>Generates comprehensive PDF reports with detailed analysis of the data.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Creating PDF reports with unit summary statistics</li>
                            <li>Including coordinate statistics in reports</li>
                            <li>Adding data quality metrics to reports</li>
                            <li>Incorporating cleaning reports into PDFs</li>
                            <li>Adding visualization placeholders to reports</li>
                        </ul>
                    </div>

                    <h4>Key Functions</h4>
                    <ul>
                        <li><code>generate_pdf_report(df, cleaning_report)</code>: Creates the complete PDF report</li>
                        <li><code>_add_unit_summary(pdf, df)</code>: Adds unit-level summary statistics</li>
                        <li><code>_add_coordinate_statistics(pdf, df)</code>: Adds detailed coordinate statistics</li>
                        <li><code>_add_data_quality_metrics(pdf, df)</code>: Adds data quality information</li>
                        <li><code>_add_cleaning_report(pdf, cleaning_report)</code>: Adds cleaning report details</li>
                    </ul>
                </div>

                <div id="unit-analyzer" class="card mb-4">
                    <h3><i class="fas fa-microscope"></i> Unit_Analyzer.py (Spatial Analysis)</h3>
                    <p>Provides specialized spatial analysis functionality for understanding geographic patterns in the data.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Processing geographic coordinate data</li>
                            <li>Identifying outliers using statistical methods</li>
                            <li>Calculating clusters and centroids using DBSCAN</li>
                            <li>Visualizing spatial patterns</li>
                        </ul>
                    </div>

                    <h4>Key Functions</h4>
                    <ul>
                        <li><code>process_data(df)</code>: Processes and validates input data</li>
                        <li><code>identify_outliers(df, method='iqr')</code>: Identifies outliers using IQR method</li>
                        <li><code>calculate_clusters_and_centroids(df, eps=0.1, min_samples=5)</code>: Applies DBSCAN clustering</li>
                        <li><code>visualize(df, clusters)</code>: Creates interactive map visualizations for units</li>
                    </ul>

                    <div class="tip-box">
                        <h4>DBSCAN Clustering</h4>
                        <p>DBSCAN (Density-Based Spatial Clustering of Applications with Noise) is a clustering algorithm that groups together points that are closely packed together, marking points that lie alone in low-density regions as outliers.</p>
                        <p>In GeoDAD, DBSCAN is used to identify clusters of units based on their geographic coordinates, which can help identify areas with high concentrations of units.</p>
                    </div>
                </div>

                <div id="cleaning-report" class="card mb-4">
                    <h3><i class="fas fa-broom"></i> enhanced_cleaning_report.py (Cleaning Reports)</h3>
                    <p>Creates visually attractive cleaning reports for the dashboard, showing what was cleaned and how.</p>

                    <div class="info-box mb-3">
                        <h4>Key Responsibilities</h4>
                        <ul>
                            <li>Creating summary cards with key metrics</li>
                            <li>Generating detailed statistics tables</li>
                            <li>Showing before/after comparisons of null values</li>
                            <li>Providing visual indicators of data quality improvements</li>
                        </ul>
                    </div>

                    <h4>Key Functions</h4>
                    <ul>
                        <li><code>create_enhanced_cleaning_report(cleaning_stats)</code>: Transforms cleaning statistics into HTML components</li>
                    </ul>

                    <div class="info-box">
                        <h4>Cleaning Report Structure</h4>
                        <p>The cleaning report includes:</p>
                        <ul>
                            <li><strong>Summary Statistics</strong>: Number of rows before and after cleaning, number of rows removed, etc.</li>
                            <li><strong>Null Value Analysis</strong>: Number of null values in each column before and after cleaning</li>
                            <li><strong>Cleaning Methods</strong>: Description of the methods used to clean the data</li>
                            <li><strong>Performance Metrics</strong>: Time taken to clean the data</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="data-processing-pipeline">
                <h2><i class="fas fa-cogs"></i> Data Processing Pipeline</h2>
                <p>The GeoDAD system follows a well-defined data processing pipeline that transforms raw data into meaningful visualizations and reports. This section explains each step in detail.</p>

                <div id="data-upload" class="card mb-4">
                    <h3><i class="fas fa-upload"></i> Step 1: Data Upload and Validation</h3>

                    <div class="ascii-diagram mb-3">
User uploads CSV → Data validation → Data summary display</div>

                    <p>The data processing pipeline begins with the user uploading a CSV file containing geographic data. The system then validates the data and displays a summary to the user.</p>

                    <div class="info-box mb-3">
                        <h4>Data Format Requirements</h4>
                        <p>The GeoDAD system expects your data to follow a specific format with required and optional columns:</p>

                        <h5>Required Columns:</h5>
                        <ul>
                            <li><code>building_place_id</code> (String): Unique identifier for the building (BPID)</li>
                            <li><code>unit_place_id</code> (String): Unique identifier for the unit (UPID)</li>
                            <li><code>scan_type</code> (String): Type of scan ("entry", "exit", or "delivery")</li>
                            <li><code>date</code> (Date): Date of the scan (YYYY-MM-DD format)</li>
                            <li><code>latitude</code> (Float): Latitude coordinate of the scan</li>
                            <li><code>longitude</code> (Float): Longitude coordinate of the scan</li>
                        </ul>

                        <h5>Optional Columns:</h5>
                        <ul>
                            <li><code>complex_id</code> (String): Identifier for a complex that contains multiple buildings</li>
                            <li><code>address_id</code> (String): Unique identifier for the address (AID)</li>
                            <li><code>delivery_hints</code> (String): Additional information to help with deliveries</li>
                        </ul>
                    </div>

                    <h4>Code Flow</h4>
                    <ol>
                        <li>User uploads CSV through <code>dcc.Upload</code> component</li>
                        <li><code>parse_contents()</code> callback processes the upload</li>
                        <li><code>data_processor.validate_data()</code> checks data structure</li>
                        <li>Data summary is displayed to the user</li>
                    </ol>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Upload Component</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The upload component is created using the <code>dcc.Upload</code> component from Dash:</p>
                                <pre><code>dcc.Upload(
    id='upload-data',
    children=html.Div([
        'Drag and Drop or ',
        html.A('Select Files')
    ]),
    style={
        'width': '100%',
        'height': '60px',
        'lineHeight': '60px',
        'borderWidth': '1px',
        'borderStyle': 'dashed',
        'borderRadius': '5px',
        'textAlign': 'center',
        'margin': '10px'
    },
    multiple=False
)</code></pre>
                                <p>This creates a drag-and-drop area where users can upload CSV files. The <code>multiple=False</code> parameter ensures that only one file can be uploaded at a time.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Parse Contents Callback</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The <code>parse_contents()</code> callback processes the uploaded file:</p>
                                <pre><code>@app.callback(
    Output('output-data-upload', 'children'),
    Input('upload-data', 'contents'),
    State('upload-data', 'filename'),
    State('upload-data', 'last_modified')
)
def parse_contents(contents, filename, date):
    if contents is not None:
        content_type, content_string = contents.split(',', 1)
        decoded = base64.b64decode(content_string)

        try:
            if 'csv' in filename:
                # Read the CSV file into a pandas DataFrame
                df = pd.read_csv(io.StringIO(decoded.decode('utf-8')))

                # Store the data in the dcc.Store component
                store_data(df)

                # Validate the data
                validation_result = data_processor.validate_data(df)

                return validation_result
            else:
                return html.Div(['Please upload a CSV file.'])
        except Exception as e:
            return html.Div(['Error processing this file: ' + str(e)])

    return html.Div('Upload a CSV file to begin.')</code></pre>
                                <p>This callback decodes the uploaded file, reads it into a pandas DataFrame, stores it in a <code>dcc.Store</code> component, validates it, and returns the validation result to display to the user.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Data Validation</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The <code>validate_data()</code> function checks if the uploaded data has the required structure:</p>
                                <pre><code>def validate_data(df):
    # Check if required columns are present
    required_columns = ['unit_place_id', 'building_place_id', 'date']
    coordinate_columns = ['enter_latitude', 'enter_longitude', 'exit_latitude', 'exit_longitude', 'scan_latitude', 'scan_longitude']

    missing_columns = [col for col in required_columns if col not in df.columns]
    missing_coord_columns = [col for col in coordinate_columns if col not in df.columns]

    if missing_columns:
        return html.Div([
            html.H5('Error: Missing Required Columns'),
            html.P(f'The following required columns are missing: {", ".join(missing_columns)}'),
            html.P('Please make sure your CSV file includes all required columns.')
        ])

    if len(missing_coord_columns) == len(coordinate_columns):
        return html.Div([
            html.H5('Error: Missing Coordinate Columns'),
            html.P('No coordinate columns found in the uploaded file.'),
            html.P('Please make sure your CSV file includes at least one set of coordinate columns.')
        ])

    # Check data types
    for col in [c for c in coordinate_columns if c in df.columns]:
        if not pd.api.types.is_numeric_dtype(df[col]):
            return html.Div([
                html.H5('Error: Invalid Data Type'),
                html.P(f'Column {col} should contain numeric values.'),
                html.P('Please check your data and try again.')
            ])

    # If all checks pass, return a summary of the data
    return html.Div([
        html.H5('Data Validation Successful'),
        html.P(f'Number of rows: {len(df)}'),
        html.P(f'Number of columns: {len(df.columns)}'),
        html.P(f'Columns: {", ".join(df.columns)}'),
        html.Button('Clean the input file', id='clean-button', n_clicks=0)
    ])</code></pre>
                                <p>This function checks if the required columns are present, if the coordinate columns have numeric values, and returns a summary of the data if all checks pass.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="data-cleaning" class="card mb-4">
                    <h3><i class="fas fa-broom"></i> Step 2: Data Cleaning</h3>

                    <div class="ascii-diagram mb-3">
User clicks "Clean Data" → Data cleaning → Cleaning report display</div>

                    <p>Once the data is uploaded and validated, the user can clean it by clicking the "Clean the input file" button. The system then performs various cleaning operations and displays a report of what was cleaned.</p>

                    <h4>Code Flow</h4>
                    <ol>
                        <li>User clicks "Clean the input file" button</li>
                        <li><code>clean_data()</code> callback is triggered</li>
                        <li><code>data_processor.clean_data()</code> performs cleaning operations</li>
                        <li><code>create_enhanced_cleaning_report()</code> generates visual report</li>
                        <li>Cleaned data is stored in <code>dcc.Store</code> component</li>
                    </ol>

                    <div class="info-box mb-3">
                        <h4>Cleaning Operations</h4>
                        <p>The data cleaning process performs the following operations:</p>
                        <ul>
                            <li><strong>Handling Missing Values</strong>: Fills or removes missing values in coordinate columns</li>
                            <li><strong>Calculating Missing Centroids</strong>: Calculates centroids for units with missing coordinates</li>
                            <li><strong>Removing Duplicates</strong>: Removes duplicate entries based on key columns</li>
                            <li><strong>Standardizing Data Types</strong>: Ensures consistent data types across columns</li>
                            <li><strong>Handling Outliers</strong>: Identifies and handles outliers in coordinate data</li>
                        </ul>
                    </div>

                    <div class="tip-box">
                        <h4>Cleaning Report</h4>
                        <p>The cleaning report provides a detailed summary of what was cleaned and how. It includes:</p>
                        <ul>
                            <li>Number of rows before and after cleaning</li>
                            <li>Number of null values before and after cleaning</li>
                            <li>Number of duplicates removed</li>
                            <li>Number of outliers identified and handled</li>
                            <li>Time taken to clean the data</li>
                        </ul>
                        <p>This report helps users understand what changes were made to their data during the cleaning process.</p>
                    </div>
                </div>

                <div id="visualization-generation" class="card mb-4">
                    <h3><i class="fas fa-chart-bar"></i> Step 3: Visualization Generation</h3>

                    <div class="ascii-diagram mb-3">
User clicks visualization button → Data processing → Visualization display</div>

                    <p>After the data is cleaned, the user can generate various visualizations to explore patterns and relationships in the data.</p>

                    <h4>Code Flow</h4>
                    <ol>
                        <li>User clicks a visualization button (e.g., "Hierarchical Treemap")</li>
                        <li>Corresponding callback is triggered</li>
                        <li><code>viz_handler</code> method creates the visualization</li>
                        <li>Visualization is displayed in the appropriate container</li>
                    </ol>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Hierarchical Treemap</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The hierarchical treemap is a key visualization that shows the hierarchical relationship between complexes, buildings, and units:</p>
                                <pre><code>def create_hierarchical_treemap(df):
    # Create a hierarchical DataFrame for the treemap
    treemap_data = []

    # Add complex level if complex_id column exists
    if 'complex_id' in df.columns:
        # Add complex nodes
        for complex_id in df['complex_id'].unique():
            complex_df = df[df['complex_id'] == complex_id]
            treemap_data.append({
                'id': f'complex-{complex_id}',
                'parent': '',
                'value': len(complex_df),
                'level': 'complex',
                'entry_scans': len(complex_df[complex_df['scan_type'] == 'entry']),
                'exit_scans': len(complex_df[complex_df['scan_type'] == 'exit']),
                'delivery_scans': len(complex_df[complex_df['scan_type'] == 'delivery'])
            })

        # Add building nodes with complex as parent
        for building_id in df['building_place_id'].unique():
            building_df = df[df['building_place_id'] == building_id]
            complex_id = building_df['complex_id'].iloc[0] if not building_df.empty else None

            if complex_id is not None:
                treemap_data.append({
                    'id': f'building-{building_id}',
                    'parent': f'complex-{complex_id}',
                    'value': len(building_df),
                    'level': 'building',
                    'entry_scans': len(building_df[building_df['scan_type'] == 'entry']),
                    'exit_scans': len(building_df[building_df['scan_type'] == 'exit']),
                    'delivery_scans': len(building_df[building_df['scan_type'] == 'delivery'])
                })
    else:
        # Add building nodes without complex parent
        for building_id in df['building_place_id'].unique():
            building_df = df[df['building_place_id'] == building_id]
            treemap_data.append({
                'id': f'building-{building_id}',
                'parent': '',
                'value': len(building_df),
                'level': 'building',
                'entry_scans': len(building_df[building_df['scan_type'] == 'entry']),
                'exit_scans': len(building_df[building_df['scan_type'] == 'exit']),
                'delivery_scans': len(building_df[building_df['scan_type'] == 'delivery'])
            })

    # Add unit nodes
    for unit_id in df['unit_place_id'].unique():
        unit_df = df[df['unit_place_id'] == unit_id]
        building_id = unit_df['building_place_id'].iloc[0] if not unit_df.empty else None

        if building_id is not None:
            treemap_data.append({
                'id': f'unit-{unit_id}',
                'parent': f'building-{building_id}',
                'value': len(unit_df),
                'level': 'unit',
                'entry_scans': len(unit_df[unit_df['scan_type'] == 'entry']),
                'exit_scans': len(unit_df[unit_df['scan_type'] == 'exit']),
                'delivery_scans': len(unit_df[unit_df['scan_type'] == 'delivery'])
            })

    # Create the treemap figure
    fig = go.Figure(go.Treemap(
        ids=[item['id'] for item in treemap_data],
        parents=[item['parent'] for item in treemap_data],
        values=[item['value'] for item in treemap_data],
        text=[f"{item['id']} ({item['value']} scans)" for item in treemap_data],
        hovertemplate='<b>%{text}</b><br>Entry Scans: %{customdata[0]}<br>Exit Scans: %{customdata[1]}<br>Delivery Scans: %{customdata[2]}<extra></extra>',
        customdata=[[item['entry_scans'], item['exit_scans'], item['delivery_scans']] for item in treemap_data],
        marker=dict(
            colors=[get_color_for_level(item['level']) for item in treemap_data],
            line=dict(width=2)
        )
    ))

    fig.update_layout(
        title='Hierarchical Treemap of Scans',
        margin=dict(t=50, l=0, r=0, b=0),
        height=600
    )

    return fig</code></pre>
                                <p>This function creates a hierarchical treemap visualization that shows the relationship between complexes, buildings, and units, with the size of each rectangle representing the number of scans.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="map-visualization" class="card mb-4">
                    <h3><i class="fas fa-map"></i> Step 4: Map Visualization</h3>

                    <div class="ascii-diagram mb-3">
User clicks "Show Map" → Map initialization → Interactive map display</div>

                    <p>The map visualization is a key component of the GeoDAD system, allowing users to explore geographic data in an interactive way.</p>

                    <h4>Code Flow</h4>
                    <ol>
                        <li>User clicks "Show the Cleaned Data on Map" button</li>
                        <li><code>show_map()</code> callback is triggered</li>
                        <li><code>map_handler.initialize_map()</code> creates the map</li>
                        <li>Interactive map is displayed with filtering controls</li>
                    </ol>

                    <div class="info-box mb-3">
                        <h4>Map Features</h4>
                        <p>The interactive map includes the following features:</p>
                        <ul>
                            <li><strong>Multiple Base Maps</strong>: Satellite, street, hybrid options</li>
                            <li><strong>Data Layers</strong>: Entry, exit, delivery scan points</li>
                            <li><strong>Clustering</strong>: Automatic clustering of nearby points</li>
                            <li><strong>Filtering</strong>: Filter by complex, building, unit, date, scan type</li>
                            <li><strong>Road Data</strong>: Integration of road data with customizable styles</li>
                            <li><strong>Popups</strong>: Detailed information about each point on click</li>
                        </ul>
                    </div>
                </div>

                <div id="report-generation" class="card mb-4">
                    <h3><i class="fas fa-file-pdf"></i> Step 5: Report Generation</h3>

                    <div class="ascii-diagram mb-3">
User clicks "Generate Report" → Report creation → PDF download</div>

                    <p>The final step in the data processing pipeline is generating a comprehensive PDF report with detailed analysis of the data.</p>

                    <h4>Code Flow</h4>
                    <ol>
                        <li>User clicks "Generate EDA Report" button</li>
                        <li><code>generate_report()</code> callback is triggered</li>
                        <li><code>report_generator.generate_pdf_report()</code> creates the PDF</li>
                        <li>PDF is made available for download</li>
                    </ol>

                    <div class="info-box mb-3">
                        <h4>Report Contents</h4>
                        <p>The PDF report includes the following sections:</p>
                        <ul>
                            <li><strong>Title Page</strong>: Report title and generation timestamp</li>
                            <li><strong>Unit Summary</strong>: Key metrics about units in the dataset</li>
                            <li><strong>Coordinate Statistics</strong>: Detailed analysis of geographic coordinates</li>
                            <li><strong>Data Quality Metrics</strong>: Completeness and missing value analysis</li>
                            <li><strong>Cleaning Report</strong>: Details of the cleaning process</li>
                            <li><strong>Visualization Placeholders</strong>: References to interactive visualizations</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="visualization-components">
                <h2><i class="fas fa-chart-pie"></i> Visualization Components</h2>
                <p>The GeoDAD system provides a variety of visualization components to help users explore and understand their geographic data. This section explains each visualization component in detail.</p>

                <div id="hierarchical-treemap" class="card mb-4">
                    <h3><i class="fas fa-sitemap"></i> Hierarchical Treemap</h3>
                    <p>The hierarchical treemap is the central visualization component that shows the hierarchical relationship between complexes, buildings, and units.</p>

                    <div class="diagram-container">
                        <img src="images/hierarchical_treemap.svg" alt="Hierarchical Treemap Visualization" class="diagram-image">
                        <p class="diagram-caption">Figure 4: Example of a hierarchical treemap visualization</p>
                    </div>

                    <div class="info-box mb-3">
                        <h4>Key Features</h4>
                        <ul>
                            <li><strong>Dynamic Sizing</strong>: Rectangle size represents the number of scans</li>
                            <li><strong>Color Coding</strong>: Different colors for different entity types</li>
                            <li><strong>Interactive Drilling</strong>: Click to drill down into the hierarchy</li>
                            <li><strong>Search Functionality</strong>: Search for specific entities</li>
                            <li><strong>Filtering</strong>: Click to filter other visualizations</li>
                        </ul>
                    </div>

                    <div class="warning-box mb-3">
                        <h4>Important Note</h4>
                        <p>The hierarchical treemap is the central control mechanism that synchronizes with all other visualizations in the dashboard. It should remain visible at all times to ensure consistent filtering across the platform.</p>
                    </div>

                    <h4>Implementation Details</h4>
                    <ul>
                        <li>Created using <code>go.Treemap</code> from Plotly</li>
                        <li>Hierarchical data structure built from the input DataFrame</li>
                        <li>Interactive elements added through Plotly configuration</li>
                        <li>Search functionality implemented through custom callbacks</li>
                    </ul>

                    <div class="tip-box">
                        <h4>Using the Treemap</h4>
                        <p>To get the most out of the hierarchical treemap:</p>
                        <ol>
                            <li><strong>Click on a rectangle</strong> to drill down into that entity</li>
                            <li><strong>Click the up arrow</strong> to go back up the hierarchy</li>
                            <li><strong>Use the search box</strong> to find specific entities</li>
                            <li><strong>Click the filter button</strong> after selecting an entity to filter other visualizations</li>
                            <li><strong>Hover over a rectangle</strong> to see detailed information about that entity</li>
                        </ol>
                    </div>
                </div>

                <div id="interactive-maps" class="card mb-4">
                    <h3><i class="fas fa-map-marked-alt"></i> Maps and Spatial Visualization</h3>
                    <p>Interactive maps provide a geographic view of the data, allowing users to explore spatial patterns and relationships.</p>

                    <div class="info-box mb-3">
                        <h4>Key Features</h4>
                        <ul>
                            <li><strong>Multiple Base Maps</strong>: Satellite, street, hybrid options</li>
                            <li><strong>Data Layers</strong>: Entry, exit, delivery scan points</li>
                            <li><strong>Clustering</strong>: Automatic clustering of nearby points</li>
                            <li><strong>Filtering</strong>: Filter by complex, building, unit, date, scan type</li>
                            <li><strong>Road Data</strong>: Integration of road data with customizable styles</li>
                            <li><strong>Popups</strong>: Detailed information about each point on click</li>
                        </ul>
                    </div>

                    <h4>Implementation Details</h4>
                    <ul>
                        <li>Built using Leaflet.js through custom HTML/JS</li>
                        <li>Map initialization and updates handled by <code>map_handler.py</code></li>
                        <li>Custom layer controls for toggling data visibility</li>
                        <li>Road data fetched through Flask endpoints</li>
                    </ul>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Map Initialization</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>The map is initialized with the following JavaScript code:</p>
                                <pre><code>// Initialize the map
var map = L.map('map').setView([center_lat, center_lng], zoom_level);

// Add base map layers
var satellite = L.tileLayer('https://{s}.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}', {
    maxZoom: 20,
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
});

var streets = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
});

var hybrid = L.tileLayer('https://{s}.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}', {
    maxZoom: 20,
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
});

// Add the base map to the map
satellite.addTo(map);

// Create a layer control
var baseMaps = {
    "Satellite": satellite,
    "Streets": streets,
    "Hybrid": hybrid
};

L.control.layers(baseMaps).addTo(map);</code></pre>
                                <p>This code initializes a Leaflet map with satellite, street, and hybrid base map options, and adds a layer control for switching between them.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Adding Data Layers</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Data layers are added to the map with the following JavaScript code:</p>
                                <pre><code>// Create marker clusters for each scan type
var enterCluster = L.markerClusterGroup();
var exitCluster = L.markerClusterGroup();
var deliveryCluster = L.markerClusterGroup();

// Add markers to the appropriate cluster
for (var i = 0; i < data.length; i++) {
    var point = data[i];
    var marker;

    if (point.scan_type === 'entry') {
        marker = L.marker([point.latitude, point.longitude], {icon: enterIcon});
        marker.bindPopup(createPopupContent(point));
        enterCluster.addLayer(marker);
    } else if (point.scan_type === 'exit') {
        marker = L.marker([point.latitude, point.longitude], {icon: exitIcon});
        marker.bindPopup(createPopupContent(point));
        exitCluster.addLayer(marker);
    } else if (point.scan_type === 'delivery') {
        marker = L.marker([point.latitude, point.longitude], {icon: deliveryIcon});
        marker.bindPopup(createPopupContent(point));
        deliveryCluster.addLayer(marker);
    }
}

// Add the clusters to the map
map.addLayer(enterCluster);
map.addLayer(exitCluster);
map.addLayer(deliveryCluster);

// Create an overlay control for the clusters
var overlayMaps = {
    "Entry Scans": enterCluster,
    "Exit Scans": exitCluster,
    "Delivery Scans": deliveryCluster
};

L.control.layers(baseMaps, overlayMaps).addTo(map);</code></pre>
                                <p>This code creates marker clusters for each scan type (entry, exit, delivery), adds markers to the appropriate cluster, and adds the clusters to the map with a layer control for toggling their visibility.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Road Data Integration</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Road data is integrated with the following JavaScript code:</p>
                                <pre><code>// Fetch road data from the server
fetch('/get_roads')
    .then(response => response.json())
    .then(data => {
        // Create a GeoJSON layer for the roads
        var roadsLayer = L.geoJSON(data, {
            style: function(feature) {
                // Style the roads based on their type
                if (feature.properties.highway === 'service') {
                    return {
                        color: '#ffffff',
                        weight: 3,
                        opacity: 0.8
                    };
                } else if (feature.properties.highway === 'residential') {
                    return {
                        color: '#f0f0f0',
                        weight: 2,
                        opacity: 0.7
                    };
                } else {
                    return {
                        color: '#cccccc',
                        weight: 1,
                        opacity: 0.6
                    };
                }
            },
            onEachFeature: function(feature, layer) {
                // Add a popup to each road
                layer.bindPopup(createRoadPopupContent(feature.properties));
            }
        });

        // Add the roads layer to the map
        roadsLayer.addTo(map);

        // Add the roads layer to the overlay control
        overlayMaps["Roads"] = roadsLayer;
        L.control.layers(baseMaps, overlayMaps).addTo(map);
    });</code></pre>
                                <p>This code fetches road data from the server, creates a GeoJSON layer for the roads with custom styling based on road type, adds popups to each road, and adds the roads layer to the map with a layer control for toggling its visibility.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="statistical-charts" class="card mb-4">
                    <h3><i class="fas fa-chart-line"></i> Statistical Visualizations</h3>
                    <p>Statistical visualizations provide insights into the distribution, relationships, and patterns in the data.</p>

                    <div class="info-box mb-3">
                        <h4>Key Visualizations</h4>
                        <ul>
                            <li><strong>Histograms</strong>: Distribution of numeric variables</li>
                            <li><strong>Scatter Plots</strong>: Relationships between variables</li>
                            <li><strong>Correlation Heatmaps</strong>: Correlation between numeric variables</li>
                            <li><strong>3D Scatter Plots</strong>: Spatial-temporal patterns</li>
                            <li><strong>Animated Time Series</strong>: Temporal patterns over time</li>
                        </ul>
                    </div>

                    <h4>Implementation Details</h4>
                    <ul>
                        <li>Created using Plotly Express and Graph Objects</li>
                        <li>Consistent styling across visualizations</li>
                        <li>Interactive elements for data exploration</li>
                        <li>Responsive design for different screen sizes</li>
                    </ul>

                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Histograms</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Histograms are created with the following code:</p>
                                <pre><code>def create_histograms(df, columns=None):
    """Create histograms for the specified columns."""
    if columns is None:
        # Use numeric columns by default
        columns = df.select_dtypes(include=['number']).columns.tolist()

    # Create a subplot for each column
    fig = make_subplots(rows=len(columns), cols=1, subplot_titles=columns)

    # Add a histogram for each column
    for i, col in enumerate(columns):
        fig.add_trace(
            go.Histogram(
                x=df[col],
                name=col,
                marker_color='#3498db',
                opacity=0.75
            ),
            row=i+1, col=1
        )

    # Update the layout
    fig.update_layout(
        height=300 * len(columns),
        width=800,
        title_text="Histograms",
        showlegend=False
    )

    return fig</code></pre>
                                <p>This function creates a histogram for each specified column in the DataFrame, arranging them vertically in a subplot.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>Scatter Plots</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>Scatter plots are created with the following code:</p>
                                <pre><code>def create_scatter_plots(df, x_col, y_col, color_col=None, size_col=None):
    """Create a scatter plot for the specified columns."""
    # Create the scatter plot
    fig = px.scatter(
        df,
        x=x_col,
        y=y_col,
        color=color_col,
        size=size_col,
        hover_name=df.index if df.index.name else None,
        hover_data=df.columns,
        title=f"{x_col} vs {y_col}"
    )

    # Update the layout
    fig.update_layout(
        height=600,
        width=800,
        xaxis_title=x_col,
        yaxis_title=y_col
    )

    return fig</code></pre>
                                <p>This function creates a scatter plot for the specified x and y columns, with optional color and size variables.</p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span>3D Scatter Plots</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p>3D scatter plots are created with the following code:</p>
                                <pre><code>def create_3d_scatter(df, x_col, y_col, z_col, color_col=None):
    """Create a 3D scatter plot for the specified columns."""
    # Create the 3D scatter plot
    fig = px.scatter_3d(
        df,
        x=x_col,
        y=y_col,
        z=z_col,
        color=color_col,
        hover_name=df.index if df.index.name else None,
        hover_data=df.columns,
        title=f"3D Scatter Plot: {x_col}, {y_col}, {z_col}"
    )

    # Update the layout
    fig.update_layout(
        height=800,
        width=1000,
        scene=dict(
            xaxis_title=x_col,
            yaxis_title=y_col,
            zaxis_title=z_col
        )
    )

    return fig</code></pre>
                                <p>This function creates a 3D scatter plot for the specified x, y, and z columns, with an optional color variable.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="common-faqs">
                <h2><i class="fas fa-question-circle"></i> Common FAQs</h2>
                <p>This section contains frequently asked questions about the GeoDAD system, answered with detailed explanations suitable for both technical and non-technical stakeholders.</p>

                <div class="info-box mb-4">
                    <h4><i class="fas fa-info-circle"></i> About These FAQs</h4>
                    <p>Each FAQ is based on thorough codebase research and provides business-focused insights. Click on any question to expand the detailed answer with practical examples and business value explanations.</p>
                </div>

                <div class="card mb-4">
                    <h3>FAQ Categories</h3>
                    <div class="accordion">
                        <!-- FAQ items will be added here as questions are answered -->
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-info-circle"></i> Getting Started</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><em>Questions and answers about getting started with GeoDAD will be added here as they are researched and answered.</em></p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-chart-bar"></i> Data Analysis & Visualization</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><em>Questions about data analysis capabilities, visualization features, and business insights will be added here.</em></p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-map-marked-alt"></i> Mapping & Geospatial Features</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><em>Questions about interactive maps, filtering, and geospatial analysis will be added here.</em></p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-cogs"></i> Technical Implementation</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><em>Technical questions about system architecture, data processing, and advanced features will be added here.</em></p>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-business-time"></i> Business Value & Decision Making</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <p><em>Questions about business value, ROI, decision-making insights, and strategic benefits will be added here.</em></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tip-box">
                    <h4><i class="fas fa-lightbulb"></i> How to Use This FAQ Section</h4>
                    <ul>
                        <li><strong>Interactive Format:</strong> Click on any question header to expand the detailed answer</li>
                        <li><strong>Business-Focused:</strong> Answers are written for program managers and non-technical stakeholders</li>
                        <li><strong>Research-Based:</strong> Each answer is backed by thorough codebase analysis</li>
                        <li><strong>Practical Examples:</strong> Real-world scenarios and use cases are provided</li>
                        <li><strong>Decision Support:</strong> Focus on business value and actionable insights</li>
                    </ul>
                </div>
            </section>

            <section id="glossary">
                <h2><i class="fas fa-book"></i> Glossary</h2>
                <p>This glossary provides definitions for key terms and concepts used in the GeoDAD system.</p>

                <div class="card mb-4">
                    <h3>Key Terms</h3>

                    <dl>
                        <dt>AID (Address ID)</dt>
                        <dd>A unique identifier for an address, which may be associated with a unit.</dd>

                        <dt>BPID (Building Place ID)</dt>
                        <dd>A unique identifier for a building, which contains multiple units.</dd>

                        <dt>Complex</dt>
                        <dd>The highest level in the hierarchy, representing a collection of buildings (e.g., an apartment complex, shopping mall, or business park).</dd>

                        <dt>DBSCAN</dt>
                        <dd>Density-Based Spatial Clustering of Applications with Noise, a clustering algorithm used to identify clusters of units based on their geographic coordinates.</dd>

                        <dt>Delivery Scan</dt>
                        <dd>A scan recorded when making a delivery to a unit.</dd>

                        <dt>Entry Scan</dt>
                        <dd>A scan recorded when entering a location.</dd>

                        <dt>Exit Scan</dt>
                        <dd>A scan recorded when leaving a location.</dd>

                        <dt>Hierarchical Treemap</dt>
                        <dd>A visualization that shows the hierarchical relationship between complexes, buildings, and units, with the size of each rectangle representing the number of scans.</dd>

                        <dt>RE (Road Entry) Point</dt>
                        <dd>A point on or near a road where an entry or exit scan was recorded, used to identify the most likely entry/exit points for a building.</dd>

                        <dt>UPID (Unit Place ID)</dt>
                        <dd>A unique identifier for a unit, which is contained within a building.</dd>
                    </dl>
                </div>
            </section>

            <section id="spatial-analysis">
                <h2><i class="fas fa-globe"></i> Spatial Analysis</h2>
                <p>The GeoDAD system provides advanced spatial analysis capabilities for understanding geographic patterns and relationships in the data.</p>

                <div class="card mb-4">
                    <h3>Clustering Analysis</h3>
                    <p>Clustering analysis helps identify groups of units that are geographically close to each other, which can be useful for route optimization and service area planning.</p>

                    <div class="info-box mb-3">
                        <h4>Key Clustering Techniques</h4>
                        <ul>
                            <li><strong>DBSCAN</strong>: Density-Based Spatial Clustering of Applications with Noise</li>
                            <li><strong>K-means</strong>: Partitioning the data into K clusters</li>
                            <li><strong>Hierarchical Clustering</strong>: Building a hierarchy of clusters</li>
                        </ul>
                    </div>

                    <h4>Implementation Details</h4>
                    <pre><code>def perform_dbscan_clustering(df, eps=0.001, min_samples=5):
    """Perform DBSCAN clustering on the geographic coordinates."""
    # Extract coordinates
    coords = df[["latitude", "longitude"]].values

    # Perform DBSCAN clustering
    dbscan = DBSCAN(eps=eps, min_samples=min_samples, metric="haversine")
    df["cluster"] = dbscan.fit_predict(coords)

    # Count the number of clusters (excluding noise points)
    n_clusters = len(set(df["cluster"])) - (1 if -1 in df["cluster"] else 0)

    # Calculate cluster statistics
    cluster_stats = df.groupby("cluster").agg({
        "unit_place_id": "nunique",
        "building_place_id": "nunique",
        "latitude": ["mean", "std"],
        "longitude": ["mean", "std"]
    })

    return df, cluster_stats, n_clusters</code></pre>
                </div>

                <div class="card mb-4">
                    <h3>Centroid Calculation</h3>
                    <p>Centroid calculation is used to determine the geographic center of a group of units, which can be useful for identifying optimal delivery locations.</p>

                    <h4>Implementation Details</h4>
                    <pre><code>def calculate_centroids(df, group_by="building_place_id"):
    """Calculate centroids for groups of units."""
    # Group by the specified column
    grouped = df.groupby(group_by)

    # Calculate centroids
    centroids = grouped.agg({
        "latitude": "mean",
        "longitude": "mean",
        "unit_place_id": "nunique"
    })

    # Rename columns
    centroids = centroids.rename(columns={
        "latitude": "centroid_latitude",
        "longitude": "centroid_longitude",
        "unit_place_id": "unit_count"
    })

    return centroids</code></pre>
                </div>

                <div class="card mb-4">
                    <h3>Road Entry Point Detection</h3>
                    <p>Road entry point detection identifies the most likely points where delivery personnel enter and exit a building, which can be useful for route optimization and delivery planning.</p>

                    <h4>Implementation Details</h4>
                    <pre><code>def detect_road_entry_points(df, roads_gdf):
    """Detect road entry points for buildings."""
    # Extract entry and exit scans
    entry_scans = df[df["scan_type"] == "entry"][["building_place_id", "latitude", "longitude"]]
    exit_scans = df[df["scan_type"] == "exit"][["building_place_id", "latitude", "longitude"]]

    # Find the nearest road for each scan
    entry_points = []
    for _, scan in entry_scans.iterrows():
        point = Point(scan["longitude"], scan["latitude"])
        nearest_road = nearest_road_to_point(point, roads_gdf)
        if nearest_road is not None:
            entry_points.append({
                "building_place_id": scan["building_place_id"],
                "latitude": point.y,
                "longitude": point.x,
                "road_id": nearest_road["id"],
                "road_name": nearest_road["name"],
                "distance": nearest_road["distance"]
            })

    # Create a DataFrame of road entry points
    entry_points_df = pd.DataFrame(entry_points)

    # Group by building and find the most common entry point
    building_entry_points = entry_points_df.groupby("building_place_id").apply(
        lambda x: x.loc[x["distance"].idxmin()]
    )

    return building_entry_points</code></pre>
                </div>

                <div class="card mb-4">
                    <h3>Outlier Detection</h3>
                    <p>Outlier detection identifies units with unusual geographic patterns, which can be useful for identifying data quality issues or unusual delivery patterns.</p>

                    <h4>Implementation Details</h4>
                    <pre><code>def detect_outliers(df, method="isolation_forest"):
    """Detect outliers in the geographic coordinates."""
    # Extract coordinates
    coords = df[["latitude", "longitude"]].values

    if method == "isolation_forest":
        # Use Isolation Forest for outlier detection
        iso_forest = IsolationForest(contamination=0.05, random_state=42)
        df["outlier"] = iso_forest.fit_predict(coords)
        # Convert to boolean (True for outliers)
        df["outlier"] = df["outlier"] == -1
    elif method == "local_outlier_factor":
        # Use Local Outlier Factor for outlier detection
        lof = LocalOutlierFactor(n_neighbors=20, contamination=0.05)
        df["outlier"] = lof.fit_predict(coords)
        # Convert to boolean (True for outliers)
        df["outlier"] = df["outlier"] == -1

    # Extract outliers
    outliers = df[df["outlier"]]

    return df, outliers</code></pre>
                </div>
            </section>

            <section id="glossary">
                <h2><i class="fas fa-book"></i> Glossary</h2>
                <p>This glossary provides definitions for key terms and acronyms used throughout the GeoDAD system and documentation.</p>

                <div class="card mb-4">
                    <h3>Key Terms and Definitions</h3>
                    <div class="accordion">
                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-building"></i> Geographic Hierarchy Terms</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <dl>
                                    <dt>Complex</dt>
                                    <dd>The highest level in the geographic hierarchy, representing a collection of buildings (e.g., apartment complex, shopping mall, business park). Identified by a <code>complex_id</code>.</dd>

                                    <dt>Building</dt>
                                    <dd>A physical structure containing multiple units. Identified by a <code>building_place_id</code> (BPID).</dd>

                                    <dt>Unit</dt>
                                    <dd>An individual address or location within a building (e.g., apartment, office, store). Identified by a <code>unit_place_id</code> (UPID).</dd>

                                    <dt>BPID</dt>
                                    <dd>Building Place ID. A unique identifier for a building in the system.</dd>

                                    <dt>UPID</dt>
                                    <dd>Unit Place ID. A unique identifier for a unit in the system.</dd>

                                    <dt>AID</dt>
                                    <dd>Address ID. A unique identifier for an address associated with a unit.</dd>
                                </dl>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-qrcode"></i> Scan Types and Data Collection</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <dl>
                                    <dt>Entry Scan</dt>
                                    <dd>A scan recorded when entering a location, capturing the coordinates of the entry point.</dd>

                                    <dt>Exit Scan</dt>
                                    <dd>A scan recorded when leaving a location, capturing the coordinates of the exit point.</dd>

                                    <dt>Delivery Scan</dt>
                                    <dd>A scan recorded when making a delivery at a specific unit, capturing the coordinates of the delivery point.</dd>

                                    <dt>Delivery Hints</dt>
                                    <dd>Additional information provided to help with deliveries to a specific unit (e.g., "Use side entrance", "Leave at front desk").</dd>
                                </dl>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-map"></i> Mapping and Visualization Terms</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <dl>
                                    <dt>Hierarchical Treemap</dt>
                                    <dd>A visualization that displays hierarchical data as nested rectangles, with the size of each rectangle representing a quantitative value (e.g., number of scans).</dd>

                                    <dt>Marker Clustering</dt>
                                    <dd>A technique used to group nearby markers on a map into clusters to improve performance and readability when dealing with large numbers of points.</dd>

                                    <dt>Heatmap</dt>
                                    <dd>A visualization that uses color intensity to represent data density or magnitude across a geographic area.</dd>

                                    <dt>Choropleth Map</dt>
                                    <dd>A thematic map where areas are shaded or patterned in proportion to a statistical variable (e.g., scan density by region).</dd>

                                    <dt>Base Map</dt>
                                    <dd>The background map layer that provides geographic context (e.g., satellite imagery, street maps).</dd>

                                    <dt>Overlay</dt>
                                    <dd>A layer of data displayed on top of a base map (e.g., scan points, roads, buildings).</dd>
                                </dl>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-cogs"></i> System and Technical Terms</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <dl>
                                    <dt>GeoDAD</dt>
                                    <dd>Geographic Data Analysis Dashboard. The name of the system described in this documentation.</dd>

                                    <dt>Dash</dt>
                                    <dd>A Python framework for building web applications, used as the foundation for the GeoDAD system.</dd>

                                    <dt>Callback</dt>
                                    <dd>A function that is executed in response to a user interaction or other event in the Dash application.</dd>

                                    <dt>DataFrame</dt>
                                    <dd>A two-dimensional, size-mutable, potentially heterogeneous tabular data structure in pandas with labeled axes (rows and columns).</dd>

                                    <dt>GeoJSON</dt>
                                    <dd>A format for encoding geographic data structures using JavaScript Object Notation (JSON).</dd>

                                    <dt>Leaflet</dt>
                                    <dd>An open-source JavaScript library for interactive maps, used by dash-leaflet to provide mapping capabilities in the GeoDAD system.</dd>
                                </dl>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <div class="accordion-header">
                                <span><i class="fas fa-chart-bar"></i> Data Analysis Terms</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="accordion-content">
                                <dl>
                                    <dt>DBSCAN</dt>
                                    <dd>Density-Based Spatial Clustering of Applications with Noise. A clustering algorithm used to identify clusters of points based on density.</dd>

                                    <dt>Centroid</dt>
                                    <dd>The geometric center of a shape or group of points, calculated as the average of all points in the group.</dd>

                                    <dt>Outlier</dt>
                                    <dd>A data point that differs significantly from other observations in a dataset.</dd>

                                    <dt>Spatial Analysis</dt>
                                    <dd>The process of examining the locations, attributes, and relationships of features in spatial data through overlay and other analytical techniques.</dd>

                                    <dt>EDA</dt>
                                    <dd>Exploratory Data Analysis. An approach to analyzing datasets to summarize their main characteristics, often with visual methods.</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="conclusion">
                <h2><i class="fas fa-flag-checkered"></i> Conclusion</h2>

                <div class="card mb-4">
                    <h3>Summary</h3>
                    <p>The GeoDAD (Geographic Data Analysis Dashboard) project is a comprehensive web application designed for analyzing geographic data patterns in delivery systems. It provides a suite of tools for data validation, cleaning, visualization, and spatial analysis with a focus on hierarchical geographic data.</p>

                    <h4>Key Takeaways</h4>
                    <ol>
                        <li><strong>Modular Architecture</strong>: The system follows a component-based architecture with clear separation of concerns, making it easy to maintain and extend.</li>
                        <li><strong>Data Processing Pipeline</strong>: The application implements a robust data processing pipeline that handles validation, cleaning, and preparation of geographic data.</li>
                        <li><strong>Interactive Visualizations</strong>: The system provides a variety of interactive visualizations, including hierarchical treemaps, interactive maps, and statistical charts.</li>
                        <li><strong>Hierarchical Data Exploration</strong>: The application supports exploration of hierarchical geographic data (complex → building → unit), with filtering capabilities at each level.</li>
                        <li><strong>Spatial Analysis</strong>: The system includes advanced spatial analysis capabilities, including clustering, centroid calculation, and outlier detection.</li>
                        <li><strong>Report Generation</strong>: The application can generate comprehensive PDF reports with detailed analysis of the geographic data.</li>
                    </ol>
                </div>

                <div class="card mb-4">
                    <h3>Future Enhancements</h3>
                    <p>Potential areas for future development include:</p>
                    <ol>
                        <li><strong>Machine Learning Integration</strong>: Adding predictive analytics capabilities for route optimization and anomaly detection.</li>
                        <li><strong>Real-time Data Processing</strong>: Implementing streaming data processing for real-time analysis of geographic data.</li>
                        <li><strong>Mobile Compatibility</strong>: Enhancing the UI for better mobile device support.</li>
                        <li><strong>Advanced Filtering</strong>: Adding more sophisticated filtering options for complex data analysis scenarios.</li>
                        <li><strong>API Integration</strong>: Developing APIs for integration with other systems and applications.</li>
                    </ol>
                </div>
            </section>

            <!-- Extra space at the bottom for better readability -->
            <div style="height: 60px;"></div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight active navigation item based on scroll position
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            function highlightNavItem() {
                const scrollPosition = window.scrollY + 100; // Add offset for header

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${sectionId}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            }

            window.addEventListener('scroll', highlightNavItem);
            highlightNavItem(); // Call once on load

            // Mobile navigation toggle
            const mobileNavToggle = document.createElement('button');
            mobileNavToggle.classList.add('mobile-nav-toggle');
            mobileNavToggle.innerHTML = '<i class="fas fa-bars"></i>';
            mobileNavToggle.style.display = 'none';

            const sidebar = document.querySelector('.sidebar');
            sidebar.insertBefore(mobileNavToggle, sidebar.firstChild);

            mobileNavToggle.addEventListener('click', function() {
                sidebar.classList.toggle('expanded');
            });

            // Check if mobile view and show/hide toggle button
            function checkMobileView() {
                if (window.innerWidth <= 768) {
                    mobileNavToggle.style.display = 'block';
                    sidebar.classList.remove('expanded');
                } else {
                    mobileNavToggle.style.display = 'none';
                    sidebar.classList.remove('expanded');
                }
            }

            window.addEventListener('resize', checkMobileView);
            checkMobileView(); // Call once on load

            // Accordion functionality
            const accordionHeaders = document.querySelectorAll('.accordion-header');

            accordionHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const accordionItem = this.parentElement;
                    accordionItem.classList.toggle('active');

                    // Update the icon
                    const icon = this.querySelector('.fas');
                    if (accordionItem.classList.contains('active')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });
            });

            // Tab functionality
            const tabNavItems = document.querySelectorAll('.tab-nav-item');

            tabNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    const tabContainer = this.closest('.tabs');

                    // Remove active class from all tabs
                    tabContainer.querySelectorAll('.tab-nav-item').forEach(tab => {
                        tab.classList.remove('active');
                    });

                    tabContainer.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('active');
                    });

                    // Add active class to current tab
                    this.classList.add('active');
                    tabContainer.querySelector(`.tab-pane[data-tab="${tabId}"]`).classList.add('active');
                });
            });

            // Add copy button to code blocks
            const codeBlocks = document.querySelectorAll('pre code');

            codeBlocks.forEach(block => {
                const copyButton = document.createElement('button');
                copyButton.classList.add('copy-button');
                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                copyButton.style.position = 'absolute';
                copyButton.style.top = '5px';
                copyButton.style.right = '5px';
                copyButton.style.padding = '5px';
                copyButton.style.background = 'rgba(255, 255, 255, 0.2)';
                copyButton.style.border = 'none';
                copyButton.style.borderRadius = '3px';
                copyButton.style.cursor = 'pointer';

                block.parentElement.style.position = 'relative';
                block.parentElement.appendChild(copyButton);

                copyButton.addEventListener('click', function() {
                    const code = block.textContent;
                    navigator.clipboard.writeText(code).then(() => {
                        copyButton.innerHTML = '<i class="fas fa-check"></i>';
                        setTimeout(() => {
                            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                        }, 2000);
                    });
                });
            });

            // Add smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 20,
                            behavior: 'smooth'
                        });

                        // Update URL hash without scrolling
                        history.pushState(null, null, targetId);

                        // Close mobile navigation if open
                        if (window.innerWidth <= 768) {
                            sidebar.classList.remove('expanded');
                        }
                    }
                });
            });

            // Add back to top button
            const backToTopButton = document.createElement('button');
            backToTopButton.classList.add('back-to-top');
            backToTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
            backToTopButton.style.position = 'fixed';
            backToTopButton.style.bottom = '20px';
            backToTopButton.style.right = '20px';
            backToTopButton.style.padding = '10px';
            backToTopButton.style.background = 'var(--primary-color)';
            backToTopButton.style.color = 'white';
            backToTopButton.style.border = 'none';
            backToTopButton.style.borderRadius = '50%';
            backToTopButton.style.width = '40px';
            backToTopButton.style.height = '40px';
            backToTopButton.style.cursor = 'pointer';
            backToTopButton.style.display = 'none';
            backToTopButton.style.zIndex = '1000';
            backToTopButton.style.boxShadow = 'var(--shadow-md)';

            document.body.appendChild(backToTopButton);

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Show/hide back to top button based on scroll position
            function toggleBackToTopButton() {
                if (window.scrollY > 300) {
                    backToTopButton.style.display = 'block';
                } else {
                    backToTopButton.style.display = 'none';
                }
            }

            window.addEventListener('scroll', toggleBackToTopButton);
            toggleBackToTopButton(); // Call once on load

            // Add print button
            const printButton = document.createElement('button');
            printButton.classList.add('print-button');
            printButton.innerHTML = '<i class="fas fa-print"></i>';
            printButton.style.position = 'fixed';
            printButton.style.bottom = '70px';
            printButton.style.right = '20px';
            printButton.style.padding = '10px';
            printButton.style.background = 'var(--secondary-color)';
            printButton.style.color = 'white';
            printButton.style.border = 'none';
            printButton.style.borderRadius = '50%';
            printButton.style.width = '40px';
            printButton.style.height = '40px';
            printButton.style.cursor = 'pointer';
            printButton.style.zIndex = '1000';
            printButton.style.boxShadow = 'var(--shadow-md)';

            document.body.appendChild(printButton);

            printButton.addEventListener('click', function() {
                window.print();
            });
        });
    </script>
</body>
</html>
